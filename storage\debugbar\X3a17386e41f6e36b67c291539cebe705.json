{"__meta": {"id": "X3a17386e41f6e36b67c291539cebe705", "datetime": "2025-06-22 15:33:07", "utime": **********.530971, "method": "GET", "uri": "/admin/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.237874, "end": **********.530993, "duration": 0.293118953704834, "duration_str": "293ms", "measures": [{"label": "Booting", "start": **********.237874, "relative_start": 0, "end": **********.496312, "relative_end": **********.496312, "duration": 0.2584378719329834, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.496324, "relative_start": 0.2584500312805176, "end": **********.530995, "relative_end": 1.9073486328125e-06, "duration": 0.03467082977294922, "duration_str": "34.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24560328, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/login", "middleware": "web, guest, admin.login.guard", "controller": "App\\Http\\Controllers\\Admin\\Auth\\LoginController@showLoginForm", "as": "admin.login", "namespace": null, "prefix": "/admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FAdmin%2FAuth%2FLoginController.php&line=27\" onclick=\"\">app/Http/Controllers/Admin/Auth/LoginController.php:27-29</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0005200000000000001, "accumulated_duration_str": "520μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.518108, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mpWNsA3Q6a7fHuycPhy4srmiPvTuRWQZCCvQIUmO", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/login", "status_code": "<pre class=sf-dump id=sf-dump-1219339973 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1219339973\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-57131924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-57131924\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-801905762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-801905762\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2096214086 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkJKSWxLS092c0Z5TFBkVVJnd1JRdmc9PSIsInZhbHVlIjoiQ25PRmhwcVVQelJvd2lPMkJuRllLaGNpalZBMHFEZnRkc09NTGlZWWFzdUpieFppdzJWZ1NwMlYvQ2tpamt3WXNOWUpNV0FTaXNZWGdkeUxOdkd2K3B5VXV6QXhZVHFrYUU2a3NsNmMzUWhDSlhBQko2cEkrODlOa3dtY2R0RWYiLCJtYWMiOiI0YjlhNzk2MjJhNWEwMzBjZTlkZTkzMWZmNzgyNTI2ZGQ4NWM2NWZhNGNmNGM0MTFlYmI3YjEyNGQ0NDk0ZjY5IiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IlBMVWVLU0pyVkRTVnhEcUVJZnd1cWc9PSIsInZhbHVlIjoiVEVWVUdXSkRQYXhMbU80QVZweUNhVnY5RzF1aUIyZjZpK2ltZnpoUW01aGk2eXlweXJnTWdqQ080cHhCTUdsTmNqWGlkT1ZrZ1diUUthaHROaDBJUjZlZFRlcUR0MmY5ajlxa1RsNDhrR2FJZnI5SktxVzF0YjdPZzNTSi9qWEUiLCJtYWMiOiI1OTc0ZmJiMDBjODM0OWQ0YWI0MTg2ZTZhZTczM2VlMjdmOTE5MWJlNWJkNzYyZDEwZThlOGEyOWVlMGFhYWQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096214086\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1680237202 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mpWNsA3Q6a7fHuycPhy4srmiPvTuRWQZCCvQIUmO</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MXRlo8KGF0U5flK15ea0VLxaOBoH7d3zMWN6raXX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680237202\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-882112675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:33:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhyR1J0dWpzOGZOZnRLbWR2dEdmbEE9PSIsInZhbHVlIjoibitYV3dKVWo3T1hxczhZMW5oditqUlBKMUd5dTFMZ0lvVU54aVg0Skt0aHdUT1Q1YkRCNllPWGdJT1FDTlFYU0tNdy9meWxlaXB4bmFUVVZhRXRzWktQcEhueDFUclIwUFhmSHlMc2RtcC9GcHlBOEtQczNGa05PZnBDdDk0RVMiLCJtYWMiOiIyODJkZWU5MGYwMGNjNmY5OGM0ZjBlZjA4YmE5MzZiNzkyMGYxZjY2ZGY1YTI0YWE5ZTNjMDNmZDQ4MTJmY2NiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:33:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6Ik5zOGtVbWlaTWdCWFFNSDFQREZ0YkE9PSIsInZhbHVlIjoiaHY1R0J1cjBhc3VzQ21YVHVoZVdidXhGWkhncjQzNGMrOXlsMGxKU0VUbS9xdVZXbFpEYmlaMGFieGxjd053YnoyeFZXb0FXcmNJVXk5aTUwKzhnTVhuUzR0VTBTaFIzSWFFTzNFNXY0QSt1VzhXNEw5d2lvdmw2K0YzOHZ4QXciLCJtYWMiOiI3MzgzOWNmYmJjMGExMjY0ZDg2OTVmMWM0MWQwY2ZiODMwZmFkODU1MzhiMjNhNWQwYWU4OWZiZDBiNGEwYzkwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:33:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhyR1J0dWpzOGZOZnRLbWR2dEdmbEE9PSIsInZhbHVlIjoibitYV3dKVWo3T1hxczhZMW5oditqUlBKMUd5dTFMZ0lvVU54aVg0Skt0aHdUT1Q1YkRCNllPWGdJT1FDTlFYU0tNdy9meWxlaXB4bmFUVVZhRXRzWktQcEhueDFUclIwUFhmSHlMc2RtcC9GcHlBOEtQczNGa05PZnBDdDk0RVMiLCJtYWMiOiIyODJkZWU5MGYwMGNjNmY5OGM0ZjBlZjA4YmE5MzZiNzkyMGYxZjY2ZGY1YTI0YWE5ZTNjMDNmZDQ4MTJmY2NiIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:33:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6Ik5zOGtVbWlaTWdCWFFNSDFQREZ0YkE9PSIsInZhbHVlIjoiaHY1R0J1cjBhc3VzQ21YVHVoZVdidXhGWkhncjQzNGMrOXlsMGxKU0VUbS9xdVZXbFpEYmlaMGFieGxjd053YnoyeFZXb0FXcmNJVXk5aTUwKzhnTVhuUzR0VTBTaFIzSWFFTzNFNXY0QSt1VzhXNEw5d2lvdmw2K0YzOHZ4QXciLCJtYWMiOiI3MzgzOWNmYmJjMGExMjY0ZDg2OTVmMWM0MWQwY2ZiODMwZmFkODU1MzhiMjNhNWQwYWU4OWZiZDBiNGEwYzkwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:33:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882112675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-213561447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mpWNsA3Q6a7fHuycPhy4srmiPvTuRWQZCCvQIUmO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213561447\", {\"maxDepth\":0})</script>\n"}}