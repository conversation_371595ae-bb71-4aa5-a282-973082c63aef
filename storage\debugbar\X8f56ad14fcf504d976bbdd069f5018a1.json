{"__meta": {"id": "X8f56ad14fcf504d976bbdd069f5018a1", "datetime": "2025-06-22 15:18:26", "utime": **********.87993, "method": "GET", "uri": "/user/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.566586, "end": **********.87995, "duration": 0.31336402893066406, "duration_str": "313ms", "measures": [{"label": "Booting", "start": **********.566586, "relative_start": 0, "end": **********.810188, "relative_end": **********.810188, "duration": 0.24360203742980957, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.810199, "relative_start": 0.24361300468444824, "end": **********.879953, "relative_end": 2.86102294921875e-06, "duration": 0.06975388526916504, "duration_str": "69.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25720744, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "user.dashboard", "param_count": null, "params": [], "start": **********.863786, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/dashboard.blade.phpuser.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.866246, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.layouts.master", "param_count": null, "params": [], "start": **********.867588, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/layouts/master.blade.phpuser.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.header-asset", "param_count": null, "params": [], "start": **********.870107, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/header-asset.blade.phppartials.header-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Fheader-asset.blade.php&line=1", "ajax": false, "filename": "header-asset.blade.php", "line": "?"}}, {"name": "user.partials.side-nav", "param_count": null, "params": [], "start": **********.872268, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/partials/side-nav.blade.phpuser.partials.side-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Fside-nav.blade.php&line=1", "ajax": false, "filename": "side-nav.blade.php", "line": "?"}}, {"name": "user.partials.top-nav", "param_count": null, "params": [], "start": **********.873427, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.phpuser.partials.top-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Ftop-nav.blade.php&line=1", "ajax": false, "filename": "top-nav.blade.php", "line": "?"}}, {"name": "partials.footer-asset", "param_count": null, "params": [], "start": **********.875871, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/footer-asset.blade.phppartials.footer-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Ffooter-asset.blade.php&line=1", "ajax": false, "filename": "footer-asset.blade.php", "line": "?"}}, {"name": "admin.partials.notify", "param_count": null, "params": [], "start": **********.876336, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/notify.blade.phpadmin.partials.notify", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "route": {"uri": "GET user/dashboard", "middleware": "web, auth, verification.guard", "controller": "App\\Http\\Controllers\\User\\DashboardController@index", "as": "user.dashboard", "namespace": null, "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=23\" onclick=\"\">app/Http/Controllers/User/DashboardController.php:23-43</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00651, "accumulated_duration_str": "6.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `virtual_card_apis` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 881}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 706}], "start": **********.825133, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:20", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=20", "ajax": false, "filename": "DashboardController.php", "line": "20"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 7.988}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.836828, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 7.988, "width_percent": 10.753}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.840076, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 18.74, "width_percent": 7.527}, {"sql": "select * from `currencies` where `default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Admin/Currency.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\Currency.php", "line": 83}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.844862, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Currency.php:83", "source": {"index": 15, "namespace": null, "name": "app/Models/Admin/Currency.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\Currency.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FCurrency.php&line=83", "ajax": false, "filename": "Currency.php", "line": "83"}, "connection": "cardapp", "explain": null, "start_percent": 26.267, "width_percent": 7.22}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 3 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.847635, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:28", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=28", "ajax": false, "filename": "DashboardController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 33.487, "width_percent": 6.144}, {"sql": "select * from `transactions` where `user_id` = 3 order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 29}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.849458, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:29", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=29", "ajax": false, "filename": "DashboardController.php", "line": "29"}, "connection": "cardapp", "explain": null, "start_percent": 39.631, "width_percent": 8.602}, {"sql": "select count(*) as aggregate from `transactions` where `user_id` = 3 and `status` = 3", "type": "query", "params": [], "bindings": [3, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.851315, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:30", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=30", "ajax": false, "filename": "DashboardController.php", "line": "30"}, "connection": "cardapp", "explain": null, "start_percent": 48.233, "width_percent": 10.599}, {"sql": "select sum(`request_amount`) as aggregate from `transactions` where `user_id` = 3 and `type` = 'ADD-MONEY' and `status` = 1", "type": "query", "params": [], "bindings": [3, "ADD-MONEY", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 31}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.853169, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:31", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=31", "ajax": false, "filename": "DashboardController.php", "line": "31"}, "connection": "cardapp", "explain": null, "start_percent": 58.833, "width_percent": 8.602}, {"sql": "select count(*) as aggregate from `virtual_user_cards` where (`user_id` = 3 and `status` = 'Active')", "type": "query", "params": [], "bindings": [3, "Active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 32}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.855063, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:32", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=32", "ajax": false, "filename": "DashboardController.php", "line": "32"}, "connection": "cardapp", "explain": null, "start_percent": 67.435, "width_percent": 8.602}, {"sql": "select * from `languages` where `code` = 'ar' limit 1", "type": "query", "params": [], "bindings": ["ar"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1594}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.868516, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1594", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1594}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1594", "ajax": false, "filename": "helpers.php", "line": "1594"}, "connection": "cardapp", "explain": null, "start_percent": 76.037, "width_percent": 8.295}, {"sql": "select * from `languages` where `code` = 'ar' limit 1", "type": "query", "params": [], "bindings": ["ar"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1594}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.870835, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1594", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1594}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1594", "ajax": false, "filename": "helpers.php", "line": "1594"}, "connection": "cardapp", "explain": null, "start_percent": 84.332, "width_percent": 7.22}, {"sql": "select * from `transactions` where `user_id` = 3 order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "user.partials.top-nav", "file": "D:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.php", "line": 2}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.874143, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "user.partials.top-nav:2", "source": {"index": 14, "namespace": "view", "name": "user.partials.top-nav", "file": "D:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.php", "line": 2}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Ftop-nav.blade.php&line=2", "ajax": false, "filename": "top-nav.blade.php", "line": "2"}, "connection": "cardapp", "explain": null, "start_percent": 91.551, "width_percent": 8.449}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\VirtualCardApi": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FVirtualCardApi.php&line=1", "ajax": false, "filename": "VirtualCardApi.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUserWallet.php&line=1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/user/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750583843\n]"}, "request": {"path_info": "/user/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-886888867 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-886888867\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-752258050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-752258050\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1370459056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1370459056\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-820107757 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjNsK09JOFVRYjdqaGNMWldMWVBCWUE9PSIsInZhbHVlIjoic0NYcGNnNzV4emdvejRpV1EzeW1vWnVvSElmUWhqNklRNXRlSlFOamlQRkp3a1kyV2l0VEZ6SDRSNzgycHZ4Rm9GUll3Z0FZbkQyMGdkNGczeUlnTk1SVGd6dnBZOXpNKzZIM3JHMG9CeUdDdE01V2JqbGIzcEhHUWNPcVYydi8iLCJtYWMiOiJiODcxMzQ3YzE2NzVjM2M4MDQ1OTE3NmI4OWMxOTVlZDI1MDcxOTY5ODkwZjE3ZTNhOTgzNGRjNzA2MGIyMzkwIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IjIyRHhFejZFYWtSYzZobGtNeUd1aUE9PSIsInZhbHVlIjoiWXFqakwrWG5DYTRuWmpNT043Q2srNWlJdXBLcnRwdGIyQmJVRThxbHcraEU1OHlkVjJBUllDT3JlRTFXVmVnVXR5cWpWNVFJcW0yWUN2aThSeTQ3T1Y0SHdBRzhVa0E0VXB3TEl1WkJwanlzS08wSnA3d2dJbjdaQVZNcVZFYlkiLCJtYWMiOiJmZWYzN2NlYjU2ZWYxYzMzZjEwYjY2MjgwOTM2NzFmMmE3NGQ0ZDdlNmQ0ZjU5MWQ0YTIyMGY0M2FjODNmNzliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820107757\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-707498706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SJcBaZ7iTTWzVst4B6KdlZjJwU9k5yXwCvFs50gr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707498706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-50494745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:18:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZ6UnBMSkVCYTl0OWlSWXBOakt5SFE9PSIsInZhbHVlIjoiRXJWNTJpS1pRTExpMEJDZzlwSDFLOTRJZkNSODF5cVkrKzFMZlp0QzZKMncxbGZoaXo5NS9BbE5EdlozQ25FdTBXRHpzNHJtTmZObVVhYndMazI2c1hHTnFXRU5Xd1BEMkc3czBMVXhLWE5UNmNoT0IzWURaNVFWZFhVUC9ZWUIiLCJtYWMiOiI5YTM0YWRlOTQ5OThkNzc3YWYxOTU5NWEwZTI3NjRkNDE0ZThjMWQzMTRmMzA5MTA0NzZmNWYwMWI0YWRjMDJlIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:18:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6InNpZVhPVk5uelRoVEZtWlF2Mjc3K1E9PSIsInZhbHVlIjoidGd6bVg1RWVvUzU0YlR1aGQxU2VUaTBNSVdUMEc3QW1Hb01VbjA3L1dwdVRTRHF1Uk5uZ3d3SzFhVU50Q05RWm50RTh4T0FMMWw4eTJuZWZKTVFXRHM1akt6QVVVU1RtNzRMdTc0MXFhNEpxZ1ZFUVl3REdGWWprUTNYZ091QVYiLCJtYWMiOiI0MWYxNTdmNDEwN2U0ODk4ZTk2Mzc3ZjhhZmZhMzFkOGJiYWI5ZTdmMDMwYjRlOTgxNzNlYzU0MjBjNWYxYzFmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:18:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZ6UnBMSkVCYTl0OWlSWXBOakt5SFE9PSIsInZhbHVlIjoiRXJWNTJpS1pRTExpMEJDZzlwSDFLOTRJZkNSODF5cVkrKzFMZlp0QzZKMncxbGZoaXo5NS9BbE5EdlozQ25FdTBXRHpzNHJtTmZObVVhYndMazI2c1hHTnFXRU5Xd1BEMkc3czBMVXhLWE5UNmNoT0IzWURaNVFWZFhVUC9ZWUIiLCJtYWMiOiI5YTM0YWRlOTQ5OThkNzc3YWYxOTU5NWEwZTI3NjRkNDE0ZThjMWQzMTRmMzA5MTA0NzZmNWYwMWI0YWRjMDJlIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:18:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6InNpZVhPVk5uelRoVEZtWlF2Mjc3K1E9PSIsInZhbHVlIjoidGd6bVg1RWVvUzU0YlR1aGQxU2VUaTBNSVdUMEc3QW1Hb01VbjA3L1dwdVRTRHF1Uk5uZ3d3SzFhVU50Q05RWm50RTh4T0FMMWw4eTJuZWZKTVFXRHM1akt6QVVVU1RtNzRMdTc0MXFhNEpxZ1ZFUVl3REdGWWprUTNYZ091QVYiLCJtYWMiOiI0MWYxNTdmNDEwN2U0ODk4ZTk2Mzc3ZjhhZmZhMzFkOGJiYWI5ZTdmMDMwYjRlOTgxNzNlYzU0MjBjNWYxYzFmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:18:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50494745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-281304909 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750583843</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281304909\", {\"maxDepth\":0})</script>\n"}}