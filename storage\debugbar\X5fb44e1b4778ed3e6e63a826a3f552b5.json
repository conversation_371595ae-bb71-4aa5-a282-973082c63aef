{"__meta": {"id": "X5fb44e1b4778ed3e6e63a826a3f552b5", "datetime": "2025-06-23 15:36:31", "utime": **********.813467, "method": "GET", "uri": "/admin/profile/index", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.48503, "end": **********.813484, "duration": 0.32845401763916016, "duration_str": "328ms", "measures": [{"label": "Booting", "start": **********.48503, "relative_start": 0, "end": **********.7197, "relative_end": **********.7197, "duration": 0.23467016220092773, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.719713, "relative_start": 0.23468303680419922, "end": **********.813486, "relative_end": 2.1457672119140625e-06, "duration": 0.09377312660217285, "duration_str": "93.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26473944, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 59, "templates": [{"name": "1x admin.sections.profile.index", "param_count": null, "params": [], "start": **********.7727, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/sections/profile/index.blade.phpadmin.sections.profile.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fsections%2Fprofile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.sections.profile.index"}, {"name": "1x admin.components.page-title", "param_count": null, "params": [], "start": **********.773399, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/page-title.blade.phpadmin.components.page-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fpage-title.blade.php&line=1", "ajax": false, "filename": "page-title.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.components.page-title"}, {"name": "1x admin.components.breadcrumb", "param_count": null, "params": [], "start": **********.775354, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/breadcrumb.blade.phpadmin.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.components.breadcrumb"}, {"name": "1x admin.components.form.input-file", "param_count": null, "params": [], "start": **********.775944, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/form/input-file.blade.phpadmin.components.form.input-file", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fform%2Finput-file.blade.php&line=1", "ajax": false, "filename": "input-file.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.components.form.input-file"}, {"name": "8x admin.components.form.input", "param_count": null, "params": [], "start": **********.777038, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/form/input.blade.phpadmin.components.form.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fform%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 8, "name_original": "admin.components.form.input"}, {"name": "1x admin.layouts.master", "param_count": null, "params": [], "start": **********.781209, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/layouts/master.blade.phpadmin.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.master"}, {"name": "1x admin.partials.right-settings", "param_count": null, "params": [], "start": **********.781787, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/right-settings.blade.phpadmin.partials.right-settings", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fright-settings.blade.php&line=1", "ajax": false, "filename": "right-settings.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.right-settings"}, {"name": "1x admin.partials.side-nav-mini", "param_count": null, "params": [], "start": **********.782553, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/side-nav-mini.blade.phpadmin.partials.side-nav-mini", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fside-nav-mini.blade.php&line=1", "ajax": false, "filename": "side-nav-mini.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.side-nav-mini"}, {"name": "1x admin.partials.side-nav", "param_count": null, "params": [], "start": **********.794493, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/side-nav.blade.phpadmin.partials.side-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fside-nav.blade.php&line=1", "ajax": false, "filename": "side-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.side-nav"}, {"name": "2x admin.components.side-nav.link", "param_count": null, "params": [], "start": **********.795435, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/side-nav/link.blade.phpadmin.components.side-nav.link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fside-nav%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 2, "name_original": "admin.components.side-nav.link"}, {"name": "6x admin.components.side-nav.link-group", "param_count": null, "params": [], "start": **********.795875, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/side-nav/link-group.blade.phpadmin.components.side-nav.link-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fside-nav%2Flink-group.blade.php&line=1", "ajax": false, "filename": "link-group.blade.php", "line": "?"}, "render_count": 6, "name_original": "admin.components.side-nav.link-group"}, {"name": "31x admin.components.side-nav.dropdown-link", "param_count": null, "params": [], "start": **********.796509, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/components/side-nav/dropdown-link.blade.phpadmin.components.side-nav.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fcomponents%2Fside-nav%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}, "render_count": 31, "name_original": "admin.components.side-nav.dropdown-link"}, {"name": "1x admin.partials.footer", "param_count": null, "params": [], "start": **********.809246, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/footer.blade.phpadmin.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.footer"}, {"name": "1x admin.partials.notify", "param_count": null, "params": [], "start": **********.809618, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/notify.blade.phpadmin.partials.notify", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.notify"}, {"name": "1x admin.partials.auth-control", "param_count": null, "params": [], "start": **********.810114, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/auth-control.blade.phpadmin.partials.auth-control", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fauth-control.blade.php&line=1", "ajax": false, "filename": "auth-control.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.auth-control"}, {"name": "1x admin.partials.push-notification", "param_count": null, "params": [], "start": **********.810464, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/push-notification.blade.phpadmin.partials.push-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fpush-notification.blade.php&line=1", "ajax": false, "filename": "push-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.partials.push-notification"}]}, "route": {"uri": "GET admin/profile/index", "middleware": "web, auth:admin, app.mode, admin.role.guard", "controller": "App\\Http\\Controllers\\Admin\\ProfileController@index", "as": "admin.profile.index", "namespace": null, "prefix": "admin/profile", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FAdmin%2FProfileController.php&line=22\" onclick=\"\">app/Http/Controllers/Admin/ProfileController.php:22-31</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00599, "accumulated_duration_str": "5.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.744941, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 10.851}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.748391, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 10.851, "width_percent": 6.678}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.7516952, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 17.529, "width_percent": 10.684}, {"sql": "select * from `admin_role_permissions` where `admin_role_permissions`.`admin_role_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.753829, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 28.214, "width_percent": 7.513}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.756335, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 35.726, "width_percent": 7.513}, {"sql": "select * from `admin_notifications` where `admin_id` != 1 and `type` in ('SIDE_NAV')", "type": "query", "params": [], "bindings": [1, "SIDE_NAV"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.784274, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 14, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 43.239, "width_percent": 20.2}, {"sql": "select * from `admins` where `admins`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.786635, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 19, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 63.439, "width_percent": 9.516}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7881372, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 24, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 72.955, "width_percent": 7.679}, {"sql": "select * from `admin_notifications` where `admin_id` != 1 and `type` in ('SIDE_NAV')", "type": "query", "params": [], "bindings": [1, "SIDE_NAV"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.7897198, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 14, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 80.634, "width_percent": 6.845}, {"sql": "select * from `admins` where `admins`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.791004, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 19, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 87.479, "width_percent": 6.845}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.792248, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1123", "source": {"index": 24, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1123}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1123", "ajax": false, "filename": "helpers.php", "line": "1123"}, "connection": "cardapp", "explain": null, "start_percent": 94.324, "width_percent": 5.676}]}, "models": {"data": {"App\\Models\\Admin\\Admin": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Admin\\AdminNotification": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "App\\Models\\Admin\\AdminHasRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminHasRole.php&line=1", "ajax": false, "filename": "AdminHasRole.php", "line": "?"}}, "App\\Models\\Admin\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/profile/index\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750659774\n]", "local": "ar"}, "request": {"path_info": "/admin/profile/index", "status_code": "<pre class=sf-dump id=sf-dump-974116544 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-974116544\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-53143539 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-53143539\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858247668 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-858247668\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1400501008 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/profile/index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6IitOM0pWS2E3SWh2WDkrak1xK01JTGc9PSIsInZhbHVlIjoiSm9xbC9TS0ZsMnpmWEZWcTU4Qi9YazlvWUc2R3NQZU9KU0lzYThvQTg4MVJOeWdvVFVjTGJYSVcvdng1REZxTyIsIm1hYyI6Ijg1YzVjNTczNDliMjExMmJiMzY1OTk0MzRiMTEyZDhkODMwZmVhNzk5NDAwOTVmMWJiMWJhYjJhNjEwMjBhYTQiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6IkUvdnhuRXdFbU1rODJuOHBadmF5TkE9PSIsInZhbHVlIjoiV2pXMHY4MWpORCs3L0hoNEVJMTFNazd2TW5nTjBvVlNnOG93aFBDVEpLdkhuT2ZReGlXMzdqbmh6cDlzRzgvS004aTk5eEZSL2kwRVZ5VHVJZjNvRkdlc3kxNWZFODRlNzI4TWdLakdRMjVxK2NRSk5KcU9MYVhMaXA5djJpYXd4SWJLeXhPdDJQYmtRK2tEMGtadS9FYWhSNzdBendPRC9lYmFRZ1kvUC9ZaTQvYkg0TnZHTG1hbjRGdXBoMFNGL2kvVEs4a0luNGVkT0NkdmlYV2NSUT09IiwibWFjIjoiNDc0MDlkNDUyMGJkYjBkOWE3YmI2MWU3NmE4ZGQ2OGM2NTg5YzI3ZTMxYTIxNDU2MDMzOTBmOWZiMDg0NGEyZCIsInRhZyI6IiJ9; ip_address=eyJpdiI6IllSZnB5R245MHlnejNNb0Z6MENMWEE9PSIsInZhbHVlIjoicmkwekJvY0F0MVVqT1VGV2VObkF1blFGdHZrbHhYOEFHTjVIOFp2UHJqUnQwdkFHVjhpQ3BBMGlZL0owK1liZ1huZzIwY3pQOXFvNXpoUko1M3RTb3c9PSIsIm1hYyI6Ijg4NjBmOTUxZjdjZThhY2VjYTU0Mzg3ZDc0N2YzNTAzYWIxNDRkZjkzNzJmMDNkMmQ3NzUwZTI4NmQwMDVlM2EiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjZkamlpRmFaQlhuVmQ3VjBjSDVWakE9PSIsInZhbHVlIjoidUV6YmZRQ1UwYUNDUDZJVndkY0p0djRRSzJVRVZOd2lNcTE4cFluZmVlWGplTXR5cWN0Y0JReHNsN3BFa1lZTSIsIm1hYyI6ImQ4OTI1ODQxMWM1ODliN2JmMWYzOGExNGZhOTkyYTc1ZmQxNDQyNzA0OTkwYjkzMGIwMmFjN2U3MWI1ZWIxOWIiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IkVCdkR1QjB5c2RDdnZJZHZUQStLYWc9PSIsInZhbHVlIjoiSlFlZ2FYaVJDUkxucU9ERjBJL3VUWjZ6QWJJNkFFWnRLNWxuQ0dkVEpJZllTVUZDc0VvY2VlL0JyQTFVNmNTa2JPWlpKa0NLbFFCbVZTN0FFUFNjZlE9PSIsIm1hYyI6IjE0ZjBhMTI5YTdlZjFiMGQxN2MwY2E5NjlkNjYyYjU2ZGZiYmJhZDI2ZDBlNWM1NGQ4MTVmM2ExODg0ZDM5ZGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjJqcFcvUW10MEowRnE4alByRzlEK2c9PSIsInZhbHVlIjoic0pSVVR5VWpkRVdDSEloU1JPcFpuYWNacEFaZEVZL05KS2FYRHozMnNLaW91VDh6U2FOZTl6Wks0ekRpSXRhUk5Mdy9SYmNQeTFqTmZLS3YzTzkrZnlkYlNRNDVHeEI5V2VqWk5Yb3hDK2VkSTFRcDdEanlnR0pTTDZzT0haN3kiLCJtYWMiOiI0NTZjZGNlZWQ5ZGIyNmNlN2Q0Y2YxYmE3NTYwMzIyNWQxOWE2YWZlODM4ODdjMmNiYTZhMThkYjg4NmExYmM5IiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6ImxFb0tDNG9vSVpyQjNFSWtJMm8vMFE9PSIsInZhbHVlIjoiZFVPUkdBSWhXb3MzdjBJdExqQmsvWkFTMit0cUNtbWF5YnNlaGg4TjdPcnh1RmQ0K2FmRGh5UkU0VFM3bkJyQUdUcnZ0emduYXhnYmdVeW1xVDh1ZWhOaWpyRm8ySllmMGVsQ2RqNVNJVG5DeTFMQVNJMHc2SDBqdDBacWxudzQiLCJtYWMiOiIwN2NiMGM0NjY0MDJhY2IwNzI4MGVlYThlODcwMTY0ZDkxZGQ3N2VlOTI3NGU3YWNlMjcyNTNmMjExMjhiYjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400501008\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1027460201 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TckR1LWzJcECAEEV9U5D46L3vYjTeYlIxc6zCKj5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027460201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1644352182 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 09:36:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklXTzh4TFlnNGIyTnlPVjVTSkc3bWc9PSIsInZhbHVlIjoiWDdZWDBzcWp1Und2VVUzNFVkSHcwVWJsU1pVZGd5Qkw1TTg1eWtIK0dGOFg4RmdiVmRDNVcyTXR6OTlVQjhJQ0hsZUxVbzZNdTBLOWdHNnNNNVpjUEdoYzFNa0RHV0tBZHBxUGs2dEdMYjZoMTRkT1RHaUUxRkpJdkw2T3NSWkciLCJtYWMiOiI2MmEzN2Y2M2IyMGJiODBmOTVkMmQwYmQ3YzQxNWZiZDFlNDFlNWNjNjMzZTkwNjVhZTQ5OWVjZDc1ZjNiODdiIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IkU0Ry9vam4rV1VNb3pYcnp1SHNQOFE9PSIsInZhbHVlIjoiZGZoMDNGZnFFbGtXNUd0VklNOFhvaWg5aDl6UlJqaWI2dHBxc2VTMkQwanA4RDZNWnNWeEFGSmJwM284SStGakVPaHdOOEY5Y05CVW9hWTJ1Q2FEUGtrZ1dNZllJQytwdWRka0hHV2Z5bFVuL1VLYnRTVjU2M3JJQVRQYVpYdUYiLCJtYWMiOiJlYTA5MjgzYTcxNzgwZTZlMTZlMDQ1Y2I3OTgwZjQyOTQ3ODVmN2U4NTk0YjU0OWVmZjRjZTEwZGQ2MWVhMGUzIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklXTzh4TFlnNGIyTnlPVjVTSkc3bWc9PSIsInZhbHVlIjoiWDdZWDBzcWp1Und2VVUzNFVkSHcwVWJsU1pVZGd5Qkw1TTg1eWtIK0dGOFg4RmdiVmRDNVcyTXR6OTlVQjhJQ0hsZUxVbzZNdTBLOWdHNnNNNVpjUEdoYzFNa0RHV0tBZHBxUGs2dEdMYjZoMTRkT1RHaUUxRkpJdkw2T3NSWkciLCJtYWMiOiI2MmEzN2Y2M2IyMGJiODBmOTVkMmQwYmQ3YzQxNWZiZDFlNDFlNWNjNjMzZTkwNjVhZTQ5OWVjZDc1ZjNiODdiIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IkU0Ry9vam4rV1VNb3pYcnp1SHNQOFE9PSIsInZhbHVlIjoiZGZoMDNGZnFFbGtXNUd0VklNOFhvaWg5aDl6UlJqaWI2dHBxc2VTMkQwanA4RDZNWnNWeEFGSmJwM284SStGakVPaHdOOEY5Y05CVW9hWTJ1Q2FEUGtrZ1dNZllJQytwdWRka0hHV2Z5bFVuL1VLYnRTVjU2M3JJQVRQYVpYdUYiLCJtYWMiOiJlYTA5MjgzYTcxNzgwZTZlMTZlMDQ1Y2I3OTgwZjQyOTQ3ODVmN2U4NTk0YjU0OWVmZjRjZTEwZGQ2MWVhMGUzIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644352182\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1133270785 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/profile/index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750659774</span>\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133270785\", {\"maxDepth\":0})</script>\n"}}