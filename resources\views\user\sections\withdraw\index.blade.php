@extends('user.layouts.master')

@push('css')
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />

<!-- Toastr JS -->
<link rel="stylesheet" href="{{ asset('frontend/css/custom/deposit-widthdraw.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb', [
        'breadcrumbs' => [
            [
                'name'  => __("Dashboard"),
                'url'   => setRoute("user.dashboard"),
            ]
        ],
        'active' => __($page_title)
    ])
@endsection

@section('content')
<div class="deposit-withdraw withdraw">
    <section class="border--primary p-2 rounded-3">
        <div class="row">
            <!-- Right Card Start -->
            <div class="col-md-6 mb-3">
                <p>{{ __('Withdraw Funds') }}</p>

                <form class="bg--gray rounded-3 p-3">
                    <!-- Gateway Method -->
                    <div id="payment-step-one">
                        <label for="payment-gateway" class="mb-2">{{ __('Payment Gateway') }}</label>
                        <fieldset>
                            <select class="form-select" name="gateway" id="option_icon">
                                @foreach($payment_gateways as $gateway)
                                    <option
                                    value="{{ $gateway->alias }}"
                                        data-class="avatar"
                                        data-style="background-image: url('{{ asset('frontend/icons/' . $gateway->slug . '.svg') }}');"
                                    >
                                    {{ $gateway->name }}
                                    </option>
                                    @endforeach
                                </select>
                                <div class="selected--option--icon">
                                    <img src="{{ asset('frontend/assets/icons/usdt-active.svg') }}" />
                                </div>
                                <div class="dropdown__icon">
                                    <img src="{{ asset('frontend/assets/icons/Dropdown.svg') }}" />
                                </div>
                        </fieldset>
                        <div class="mt-2">
                            <label for="network" class="mb-2">{{ __('Networks') }}</label>
                            <fieldset>
                                <input
                                type="text"
                                name="network"
                                class="form-control network_input"
                                placeholder="{{ __('Enter Network') }}"
                                style='color:black'
                                />
                                <span class='text-danger bg-transparent req__network' style='font-size:12px;'>{{ __('required') }}</span>
                            </fieldset>
                            <button
                                id="confirm-btn"
                                type="button"
                                class="confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold"
                            >
                                {{ __('Next') }}
                            </button>
                        </div>
                    </div>

                    <div
                        id="payment-step-two"
                        class="col-md-6 bg--gray w-auto d-flex flex-column justify-content-center align-items-center rounded-3 mb-2 d-none"
                    >
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <p class="text-center">{{ __('Complete Withdrawal') }}</p>
                            <button type='button' id="back-btn" class="bg-transparent">
                                <img src="{{ asset('frontend/assets/icons/right-arrow.svg') }}" alt="{{ __('Back') }}" class="mb-3" />
                            </button>
                        </div>
                        <div class="d-flex flex-column w-100 gap-3 mb-1">
                            <div>
                                <label for="network_address">{{ __('Address') }}</label>
                                <input
                                    type="text"
                                    name="network_address"
                                    class="form-control"
                                    id="network_address"
                                    placeholder="{{ __('Enter Your Wallet Address') }}"
                                    style='color:black'
                                />
                                <span class='text-danger bg-transparent req__address' style='font-size:12px;'>{{ __('required') }}</span>
                            </div>
                            <div class="transaction_form">
                                <label for="transaction">{{ __('Transfer Amount') }}</label>
                                <input
                                type="number"
                                name="amount"
                                class="form-control amount__input"
                                id="transaction"
                                placeholder="{{ __('Enter Amount') }}"
                                style='color:black'
                                />
                                <span class='text-danger bg-transparent req__amount' style='font-size:12px;'>{{ __('required') }}</span>
                            </div>
                        </div>

                        <div class="d-flex w-100 justify-content-between my-2">
                            <span><span  class="text--primary balance__text">{{ __('Available Balance') }} </span> <span  class="text--primary balance__text hide_ltr">  USD </span> <span class="text--primary balance__text">{{ $user_wallets->balance }} <span class="text--primary balance__text hide_rtl">USD</span>  </span></span>
                            <span class="text--primary balance__text">
                                <span class="text--primary">{{ __('Charge') }}</span>
                                <span dir="rtl" class="text--primary">
                                    <span class="text--primary">USD</span>
                                    <span class="text--primary">{{ $PaymentGatewayCurrency->fixed_charge }}</span>
                                    + 
                                    <span class="text--primary">{{ $PaymentGatewayCurrency->percent_charge }}%</span>
                                </span>
                            </span>
                            
                            <span class="text--primary balance__text">
                                <span class="text--primary">{{ __('Limit') }}</span>
                                <span dir="rtl" class="text--primary">
                                    <span class="text--primary">USD</span>
                                    <span class="text--primary">{{ $PaymentGatewayCurrency->min_limit }}</span>
                                    - 
                                    <span class="text--primary">USD</span>
                                    <span class="text--primary">{{ $PaymentGatewayCurrency->max_limit }}</span>
                                </span>
                            </span>
                                              
                        </div>
                        <button
                            id="confirm-btn-done"
                            type="button"
                            class="confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold"
                        >
                            {{ __('Confirm') }}
                        </button>
                    </div>
                </form>
            </div>
            <!-- Right Card End -->

            <!-- Left Card Start -->
            <div class="col-md-6 mb-3" id="notes">
                <p>{{ __('Notes') }}</p>
                <div class="bg--gray p-3 rounded-3">
                    <section class="notes-section">
                        <ul class="p-3">
                            <li>{{ __('Ensure the correct network is selected') }}</li>
                            <li>{{ __('Ensure you have entered the correct USDT wallet address') }}</li>
                            <li>{{ __('The withdrawal process may take from a minute to 24 hours') }}</li>
                        </ul>
                    </section>
                </div>
            </div>
            <!-- Left Card End -->
        </div>
    </section>
    
    <section class="transactions d-flex flex-column">
        <div class="transactions__header" style='margin-top:20px'>
            <p>{{ __('Withdrawal Transaction History') }}</p>
        </div>
   
        <ul class="timeline w-100 dashboard-list-wrapper">
            @include('user.components.transaction-log', compact("transactions"))
        </ul>
    </section>

    <!-- Modal Start -->
    <div
        class="modal fade"
        id="withdraw"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-labelledby="withdrawLabel"
        aria-hidden="true"
        style="z-index: 9999"
    >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form>
                    <div class="modal-header">
                        <h5 class="modal-title" id="withdrawLabel">{{ __('Withdraw Funds') }}</h5>
                        <button
                            type="button"
                            class="btn-close bg-transparent"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                        >
                            <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
                        </button>
                    </div>
                    <div class="modal-body">
                        <h5 class="text-dark">
                            {{ __('Are you sure you want to withdraw') }}
                            <span class="text--primary" id="modal-amount"></span> USD {{ __('to the address') }}
                            <span class="text--primary" id="modal-address">4633</span> ?
                        </h5>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" class="btn primary__btn__base">{{ __('Yes') }}</button>
                        <button type="button" data-bs-dismiss="modal" aria-label="Close" class="btn secondary__btn__base">{{ __('No, go back') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>

    
document.addEventListener('DOMContentLoaded', function() {
    const confirmButtonStepOne = document.getElementById('confirm-btn');
    const backButton = document.getElementById('back-btn');
    const paymentStepOne = document.getElementById('payment-step-one');
    const paymentStepTwo = document.getElementById('payment-step-two');
    const modalAmount = document.getElementById('modal-amount');
    const modalAddress = document.getElementById('modal-address');
    const confirmButton = document.getElementById('confirm-btn-done');
    const netWorkInput = document.querySelector('.network_input')
    const amountWorkInput = document.querySelector('.amount__input')
    const netWorkReqMsg = document.querySelector('.req__network')
    const addressReqMsg = document.querySelector('.req__address')
    const amountReqMsg = document.querySelector('.req__amount')
    const addressInput = document.querySelector('#network_address');
    
    // ? network input
    netWorkReqMsg.style.display = 'none'
    addressReqMsg.style.display = 'none'
    amountReqMsg.style.display = 'none'
    netWorkInput.addEventListener('input',(e)=>{
        if(!!e.target.value){
            netWorkReqMsg.style.display = 'none'
        }else{
            netWorkReqMsg.style.display = 'block'
        }
    })
    // ? address input
    addressInput.addEventListener('input',(e)=>{
        if(!!e.target.value){
            addressReqMsg.style.display = 'none'
        }else{
            addressReqMsg.style.display = 'block'
        }
    })
    // ? amount input
    amountWorkInput.addEventListener('input',(e)=>{
        
        if(!!e.target.value){
            amountReqMsg.style.display = 'none'
        }else{
            amountReqMsg.style.display = 'block'
        }
    })
    // Handle the "Next" button click
    confirmButtonStepOne.addEventListener('click', function() {
        if(netWorkInput.value == ''){
            netWorkReqMsg.style.display = 'block'
            return
        }
        paymentStepOne.classList.add('d-none');
        paymentStepTwo.classList.remove('d-none');

        // Get the values from the form inputs
        const selectedGateway = document.querySelector('#option_icon option:checked');
        const selectedNetwork = document.querySelector('select[name="network"] option:checked').text;
    });

    // Handle the "Back" button click
    backButton.addEventListener('click', function() {
        paymentStepOne.classList.remove('d-none');
        paymentStepTwo.classList.add('d-none');
    });
    
    confirmButton.addEventListener('click', function() {
        const amountInput = document.getElementById('transaction').value;
        const addressInput = document.getElementById('network_address').value;
        if(!addressInput){
            addressReqMsg.style.display = 'block'
            return
        }
        if(!amountInput){
            amountReqMsg.style.display = 'block'
            return
        }
        $('#withdraw').modal('show');

        // Update the modal with the selected values
        modalAmount.textContent = amountInput;
        modalAddress.textContent = addressInput;
    });

    // Handle the form submission from the modal
    document.querySelector('#withdraw .btn.primary__btn__base').addEventListener('click', function() {
        const form = document.querySelector('form.bg--gray');
        const formData = new FormData(form);

        fetch('{{ route("user.withdraw.submit") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success('{{ __("Withdraw Money Request Sent To Admin Successfully") }}');
                setTimeout(() => {
                    window.location.href = "{{ route('user.withdraw.index') }}";
                }, 2000); // Redirect after 2 seconds
            } else {
                toastr.error('{{ __("Error: ") }}' + data.message);
            }
        })
        .catch(error => {
            console.error('{{ __("Error:") }}', error);
            toastr.error('{{ __("An error occurred. Please try again.") }}');
        });
    });
});

</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="{{ asset('frontend/js/custom/deposit-widthdraw.js') }}"></script>
<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>
@endpush
