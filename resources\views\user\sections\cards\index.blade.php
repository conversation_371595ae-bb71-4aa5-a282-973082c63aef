@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/virtual-card.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')

<style>

.custom-close{
  background-color: transparent !important;
  margin: 0 0 0 auto;
}
[dir="rtl"] .custom-close{
  margin: 0 auto 0 0 !important;
}

</style>
<div class="px-4">
  <section class="container">
    <span class="d-flex justify-content-between header">
      <h6 class="header_title">{{ __('Virtual Cards') }}</h6>
      <button
          class="primary__btn__base primary__btn"
          type="button"
          data-bs-toggle="modal"
          data-bs-target="#addCardModal"
        >
          {{ __('Create New Card') }}
        </button>
    </span>


    <!-- cards start -->

<div class="container">
    <div class="row">
        <!-- Card 1 -->
        @foreach ($cards as $index => $card)
        @php
        $image = App\Models\CardImage::where('bin', $card->platform)->first();
    @endphp
    
        <a href="{{ route('user.cards.details', ['cardID' => $card->cardId]) }}" class="col-md-12 col-lg-6 col-xl-4 mb-4">
            <div class="border border--primary rounded-3 p-2 one_card text-dark">
                <div class="d-flex align-items-center justify-content-between">
                    <!-- Card Image Section -->
                    <div class="d-flex align-items-center">
                        <img src="{{ isset($image) ? asset($image->image_path) : asset('frontend/assets/vcards/card1.svg') }}"
                             alt="card" 
                             class="img-fluid" 
                             style="height: 53px; width: 85px;">
                        <div class="p-2">
                            <p class="mb-1 fw-bold text-size-14">{{ $card->balance }}</p>
                            <p class="mb-0 fw-light text-size-12">{{ $card->cardName }}</p>
                        </div>
                    </div>
                    <!-- Arrow Icon -->
                    <img src="{{ asset('frontend/assets/down-gray-arrow.svg') }}" 
                         alt="arrow" 
                         class="rotate_arrow img-fluid" 
                         style="height: 20px; width: 20px;">
                </div>
            </div>
        </a>

        @endforeach

        <!-- Add more cards as needed -->

    </div>
</div>


    <!-- cards end -->




    <!--            card recharge modal-->
    <div class="modal fade"
      id="cardRecharge"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="cardRechargeLabel"
      aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <form action="{{ route('user.cards.rechargeCard') }}" method="POST">
            @csrf
            <input type="hidden" name="cardId" id="rechargeCardId" value="">
            <input type="hidden" name="account_uuid" id="rechargeAccountUuid" value="">

            <div class="modal-header">
              <h5 class="modal-title" id="cardRechargeLabel">{{ __('Card Recharge') }}</h5>
              <button
                style="all: unset; cursor: pointer"
                type="button"
                class="btn-close bg-transparent"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
              </button>
            </div>
            <div class="modal-body modal__body">
              <div class="form-group">
                <label for="amount">{{ __('Amount') }}</label>
                <input
                  name="amount"
                  type="number"
                  step="0.1"
                  class="form-control text-black-50"
                  id="amount"
                  aria-describedby="amountHelp"
                  placeholder="{{ __('Enter Amount') }}"
                />
                <span class="text-black-50" id='currency'> USD </span>
                <div class="modal__body__info d-flex justify-content-between">
                  <p>{{ authWalletBalance() }} USD {{ __('Available Balance') }}</p>
                </div>

                <div class="d-flex justify-content-between recharge_limits">
                  <p>{{ __('Limit') }} {{ $virtualCardCharge->min_purchase_limit }} USD - {{ $virtualCardCharge->max_purchase_limit }} USD</p>
                  <p>{{ __('Charge') }}: {{ $virtualCardCharge->fixed_recharge_fee }} USD + {{ $virtualCardCharge->percent_recharge_fee }}%</p>
                </div>
              </div>
            </div>
            <div class="modal-footer d-flex gap-1 justify-content-between">
              <button
                type="submit"
                id='submitRecharge'
                class="btn btn-primary bg--primary modal__footer__actions confirm d-flex flex-grow-1"
              >
                {{ __('Confirm') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!--            ** card recharge modal-->

    <!--            deleteCard-->
    <div
      class="modal fade"
      id="deleteCard"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="deleteCardLabel"
      aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <form action="{{ route('user.cards.blockCard') }}" method="POST">
            @csrf        
            <div class="modal-header">
              <h5 class="modal-title" id="deleteCardLabel">
                  {{ __('Delete Card') }}
              </h5>
              <button
                type="button"
                style="all: unset"
                class="btn-close bg-transparent"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
              </button>
            </div>
            <div class="modal-body modal__body">
              <h6>{{ __('Are you sure you want to delete this card?') }}</h6>
              <p>
                {{ __('If you are sure you will not use this card again and want to delete it, click on "Yes, delete it!"') }}
                <br />
                {{ __('Just remember that once deleted, you will not be able to retrieve this card or the added information.') }}
              </p>
            </div>
            <div class="modal-footer d-flex gap-1 justify-content-between">
              <input type="hidden" name="cardId" id="cardDelete" value="">
              <button
                type="button"
                class="btn btn-primary modal__footer__actions bg--primary delete_account_modal__footer__actions confirm d-flex flex-grow-1"
                data-bs-dismiss="modal"
              >
                {{ __('No, I’ll keep my card') }}
              </button>
              
              <button
                type="submit"
                class="btn btn-outline-danger modal__footer__actions danger w-25"
              >
                {{ __('Yes, delete it!') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>
  <!-- virtual card end -->


  <!-- add card modal start -->
  <div class="modal fade" id="addCardModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="addCardModalLabel" aria-hidden="true" style="z-index: 9999;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form action="{{ route('user.cards.all') }}" method="POST" onsubmit="return validateForm()">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="createCardLabel">{{ __('Create a New Card') }}</h5>
                    <button type="button" class="btn-close custom-close" data-bs-dismiss="modal" aria-label="Close">
                        <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
                    </button>
                </div>
                <div class="modal-body custom-form-t">
                    <div class="form-group">
                      <label for="cardOwnerName">{{ __('Card Owner Name') }}</label>
        <input name="name" type="text" class="form-control" id="cardOwnerName" aria-describedby="cardOwnerNameHelp" placeholder="{{ __('Card Owner Name') }}" />
        <div id="nameError" class="text-danger" style="display:none;">{{ __('Please enter the card owner name') }}</div>
                    </div>
                    <div class="transaction_form form-group">
                      <label for="transaction">{{ __('Card Balance') }}</label>
                      <input name="amount" type="number" step="0.1" class="form-control" id="transaction" placeholder="{{ __('Enter Amount') }}" />
                      <div id="amountError" class="text-danger" style="display:none;">{{ __('Please enter a valid amount') }}</div>
                      <span class="text--primary fw-normal">{{ authWalletBalance() }} USD {{ __('Available Balance') }}</span>

                      <div class="d-flex justify-content-between recharge_limits">
                        @if(app()->getLocale() == 'en')
                        <p>
                          {{ __('Limit') }} 
                          {{ max($virtualCardCharge->min_purchase_limit, $virtualCardCharge->card_min_start_balance) }} 
                          USD - {{ $virtualCardCharge->max_purchase_limit }} USD
                      </p>
                      <p>{{ __('Charge') }}: {{ $virtualCardCharge->fixed_recharge_fee }} USD + {{ $virtualCardCharge->percent_recharge_fee }}%</p>
                        @else
                        <p>
                          {{ __('Limit') }} 
                          {{ $virtualCardCharge->max_purchase_limit }} -
                          USD {{ max($virtualCardCharge->min_purchase_limit, $virtualCardCharge->card_min_start_balance) }} USD
                      </p>
                      <p>{{ __('Charge') }}: {{ $virtualCardCharge->percent_recharge_fee }} + USD  %{{ $virtualCardCharge->fixed_recharge_fee }}</p>
                        @endif
                      
                    </div>
                    </div>
                    <div class="gap-4 mt-5 card_box">
                        <h3 class="card-type mb-2">{{ __('Select Your Card') }}</h3>
                        <input type="hidden" name="type" id="cardType" value="">
                        <button type="button" class="w-auto card__border p-3 card_type" onclick="document.getElementById('cardType').value = 'Visa';">
                            <img src="{{ asset('frontend/assets/icons/visa_disable.svg') }}" alt="visa-disable">
                        </button>
                        <button type="button" class="w-auto card__border p-3 card_type" onclick="document.getElementById('cardType').value = 'MasterCard';">
                            <img src="{{ asset('frontend/assets/icons/mastercard_disable.svg') }}" alt="mastercard-disable">
                        </button>
                    </div>
                </div>
                <div class="modal-footer primary__btn">
                    <button type="submit" class="btn primary__btn__base w-100" id="submitButton" disabled>
                        {{ __('Select Your Card') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


@endsection


@push('script')

<script>
function validateAmount(input) {
    const amount = parseFloat(input.value);
    const cardMinStartBalance = parseFloat("{{ $virtualCardCharge->card_min_start_balance }}");
    const minLimit = parseFloat("{{ $virtualCardCharge->min_purchase_limit }}");
    const maxLimit = parseFloat("{{ $virtualCardCharge->max_purchase_limit }}");
    const walletBalance = parseFloat("{{ authWalletBalance() }}".replace(/,/g, ''));
    const amountError = document.getElementById('amountError');
    const submitButton = document.getElementById('submitButton');
    console.log(walletBalance)
    // Choose the larger value between card_min_start_balance and min_purchase_limit
    const actualMinLimit = Math.max(cardMinStartBalance, minLimit);

    if (isNaN(amount) || amount < actualMinLimit || amount > maxLimit) {
        // Display appropriate error message
        if (isNaN(amount)) {
            amountError.textContent = '{{ __('Please enter a valid number') }}';
        } else if (amount <= actualMinLimit) {
            amountError.textContent = `{{ __('The amount must be at least') }} ${actualMinLimit} USD.`;
        } else if (amount >= maxLimit) {
            amountError.textContent = `{{ __('The amount must not exceed') }} ${maxLimit} USD.`;
        }
        amountError.style.display = 'block';
        submitButton.disabled = true; // Disable submit button
    } else if (amount > walletBalance) {
        amountError.textContent = `{{ __('The amount must be less than your wallet balance of') }} ${walletBalance} USD.`;
        amountError.style.display = 'block';
        submitButton.disabled = true; // Disable submit button
    } else {
        amountError.textContent = '';
        amountError.style.display = 'none';
        submitButton.disabled = false; // Enable submit button
    }
}
  function validateName(input) {
      const nameError = document.getElementById('nameError');
      const submitButton = document.getElementById('submitButton');
  
      if (input.value.trim() === '') {
          nameError.style.display = 'block';
          submitButton.disabled = true; // Disable submit button
      } else {
          nameError.style.display = 'none';
      }
      // Check if both validations are correct before enabling the button
      const amountInput = document.getElementById('transaction');
      validateAmount(amountInput); // Validate amount as well
  }
  
  function validateForm() {
      const nameInput = document.getElementById('cardOwnerName');
      const amountInput = document.getElementById('transaction');
  
      // Perform validations
      validateName(nameInput); // Validate name
      validateAmount(amountInput); // Validate amount
  
      // Allow form submission only if both validations pass
      return !nameInput.disabled && !amountInput.disabled;
  }
  
  // Attach event listeners
  document.getElementById('cardOwnerName').addEventListener('input', function() {
      validateName(this);
  });
  
  document.getElementById('transaction').addEventListener('input', function() {
      validateAmount(this);
  });
  
  // Disable the button on load
  document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('submitButton').disabled = true; // Disable the button on load
  });
  </script>
  <script>
    
  $(document).ready(function() {
$('.card_type').click(function() {
    // First, reset all images to their disabled versions
    $('.card_type img').each(function() {
        const imgElement = $(this);
        const imgSrc = imgElement.attr('src');

        if (imgSrc.includes('visa_active')) {
            imgElement.attr('src', imgSrc.replace('visa_active', 'visa_disable'));
        } else if (imgSrc.includes('mastercard_active')) {
            imgElement.attr('src', imgSrc.replace('mastercard_active', 'mastercard_disable'));
        }
    });

    // Then, set the clicked button's image to the active version
    const imgElement = $(this).find('img');
    const imgSrc = imgElement.attr('src');

    if (imgSrc.includes('visa_disable')) {
        imgElement.attr('src', imgSrc.replace('visa_disable', 'visa_active'));
    } else if (imgSrc.includes('mastercard_disable')) {
        imgElement.attr('src', imgSrc.replace('mastercard_disable', 'mastercard_active'));
    }
});
});
$('.default-card-btn').on('click', function() {
    // Reset all buttons to their default state
    $('.default-card-btn').each(function() {
        $(this).find('img').css('opacity', '1'); // Make the image fully visible
        $(this).find('span').text('{{ __("Set Card as Default") }}'); // Reset the text
    });
    // Toggle the clicked button
    if ($(this).find('span').text() === '{{ __("Card is set as default") }}') {
        $(this).find('img').css('opacity', '1'); // Make the image fully visible
        $(this).find('span').text('{{ __("Set Card as Default") }}');
    } else {
        $(this).find('img').css('opacity', '0.5'); // Make the image semi-transparent
        $(this).find('span').text('{{ __("Card is set as default") }}');
    }
});


function sendCardData (cardId){
// Create a FormData object
let formData = new FormData();
formData.append('cardId', cardId);
formData.append('_token', "{{csrf_token()}}");

// Send the form data via AJAX
fetch('{{ route('user.cards.makeDefaultOrRemove') }}', {
    method: 'POST',
    body: formData, // Send the FormData object
    headers: {
        'Accept': 'application/json',
    },
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('{{ __("Card status updated successfully!") }}');
    } else {
        console.error('{{ __("Failed to update card.") }}');
    }
})
.catch(error => console.error('{{ __("Error:") }}', error));
}
document.querySelectorAll('.default-card-form').forEach(function(form) {
    form.addEventListener('submit', function(e) {
        e.preventDefault(); 

        const formData = new FormData(this);
        const url = this.getAttribute('action');

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if(data.success) {
                // Update the UI based on the response
                alert('{{ __("Card status updated successfully!") }}');
                location.reload(); // Optionally reload the page to reflect changes
            } else {
                alert('{{ __("An error occurred. Please try again.") }}');
            }
        })
        .catch(error => console.error('{{ __("Error:") }}', error));
    });
});

function setCardDetails(cardId, accountUuid) {
    document.getElementById('rechargeCardId').value = cardId;
    document.getElementById('rechargeAccountUuid').value = accountUuid;
}

function setCardToDelete(cardId) {
    document.getElementById('cardDelete').value = cardId;
}


// ! create form validation 

function validateForm() {
    var isValid = true;

    var nameInput = document.getElementById('cardOwnerName');
    var amountInput = document.getElementById('transaction');

    var nameError = document.getElementById('nameError');
    var amountError = document.getElementById('amountError');

    // Validate Name
    if (nameInput.value.trim() === "") {
        nameError.style.display = "block";
        isValid = false;
    } else {
        nameError.style.display = "none";
    }

    // Validate Amount
    if (amountInput.value.trim() === "") {
        amountError.style.display = "block";
        isValid = false;
    } else {
        amountError.style.display = "none";
    }

    return isValid;
}

function checkFormValidity() {
    var nameInput = document.getElementById('cardOwnerName');
    var amountInput = document.getElementById('transaction');
    var submitButton = document.getElementById('submitButton');

    // Check if all required fields have values
    if (nameInput.value.trim() !== "" && amountInput.value.trim() !== "") {
        submitButton.disabled = false;
    } else {
        submitButton.disabled = true;
    }
}

// Attach event listeners to input fields
document.getElementById('cardOwnerName').addEventListener('input', function() {
    checkFormValidity();
});

document.getElementById('transaction').addEventListener('input', function() {
    checkFormValidity();
});

// Initial check to set button state based on existing values
checkFormValidity();


// recharge card form validation 
function checkRechargeFormValidity() {
    var rechargeInput = document.getElementById('amount');
    var submitRecharge = document.getElementById('submitRecharge');
    
    // Check if all required fields have values
    if (rechargeInput.value.trim() !== "") {
      submitRecharge.disabled = false;
    } else {
      submitRecharge.disabled = true;
    }
  }
  checkRechargeFormValidity()
  document.getElementById('amount').addEventListener('input', function() {
  checkRechargeFormValidity();
  });

//   add image to card randomly


</script>





@endpush
