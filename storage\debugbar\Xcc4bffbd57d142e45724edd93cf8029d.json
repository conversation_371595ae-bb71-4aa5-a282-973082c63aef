{"__meta": {"id": "Xcc4bffbd57d142e45724edd93cf8029d", "datetime": "2025-06-22 14:56:29", "utime": **********.896178, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.457916, "end": **********.896199, "duration": 0.43828296661376953, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.457916, "relative_start": 0, "end": **********.800662, "relative_end": **********.800662, "duration": 0.3427460193634033, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800674, "relative_start": 0.3427579402923584, "end": **********.896201, "relative_end": 1.9073486328125e-06, "duration": 0.09552693367004395, "duration_str": "95.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26005240, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "frontend.index", "param_count": null, "params": [], "start": **********.841831, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/index.blade.phpfrontend.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "frontend.layouts.master", "param_count": null, "params": [], "start": **********.851117, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.phpfrontend.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.header-asset", "param_count": null, "params": [], "start": **********.864413, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/header-asset.blade.phppartials.header-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Fheader-asset.blade.php&line=1", "ajax": false, "filename": "header-asset.blade.php", "line": "?"}}, {"name": "frontend.partials.header", "param_count": null, "params": [], "start": **********.866602, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.phpfrontend.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "frontend.partials.account-info", "param_count": null, "params": [], "start": **********.873876, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/account-info.blade.phpfrontend.partials.account-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Faccount-info.blade.php&line=1", "ajax": false, "filename": "account-info.blade.php", "line": "?"}}, {"name": "frontend.partials.footer", "param_count": null, "params": [], "start": **********.880156, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.phpfrontend.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "partials.footer-asset", "param_count": null, "params": [], "start": **********.888525, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/footer-asset.blade.phppartials.footer-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Ffooter-asset.blade.php&line=1", "ajax": false, "filename": "footer-asset.blade.php", "line": "?"}}, {"name": "admin.partials.notify", "param_count": null, "params": [], "start": **********.891051, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/notify.blade.phpadmin.partials.notify", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}, {"name": "frontend.partials.extensions.tawk-to", "param_count": null, "params": [], "start": **********.892036, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/extensions/tawk-to.blade.phpfrontend.partials.extensions.tawk-to", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fextensions%2Ftawk-to.blade.php&line=1", "ajax": false, "filename": "tawk-to.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\SiteController@home", "namespace": null, "prefix": "", "where": [], "as": "index", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=18\" onclick=\"\">app/Http/Controllers/SiteController.php:18-22</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.005860000000000001, "accumulated_duration_str": "5.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.825551, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 9.727}, {"sql": "select * from `basic_settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\SiteController.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.831703, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "SiteController.php:19", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\SiteController.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=19", "ajax": false, "filename": "SiteController.php", "line": "19"}, "connection": "cardapp", "explain": null, "start_percent": 9.727, "width_percent": 10.41}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.8518069, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1588", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1588", "ajax": false, "filename": "helpers.php", "line": "1588"}, "connection": "cardapp", "explain": null, "start_percent": 20.137, "width_percent": 9.898}, {"sql": "select * from `site_sections` where `key` = 'site_cookie' limit 1", "type": "query", "params": [], "bindings": ["site_cookie"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Admin/SiteSections.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\SiteSections.php", "line": 19}, {"index": 24, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 4}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.854247, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "SiteSections.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/Admin/SiteSections.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\SiteSections.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSiteSections.php&line=19", "ajax": false, "filename": "SiteSections.php", "line": "19"}, "connection": "cardapp", "explain": null, "start_percent": 30.034, "width_percent": 7.509}, {"sql": "select * from `setup_seos` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 5}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.855802, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.master:5", "source": {"index": 18, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=5", "ajax": false, "filename": "master.blade.php", "line": "5"}, "connection": "cardapp", "explain": null, "start_percent": 37.543, "width_percent": 5.973}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.86188, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 43.515, "width_percent": 15.188}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.865057, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 58.703, "width_percent": 8.362}, {"sql": "select * from `setup_pages` where `status` = 1 and `type` = 'setup-page'", "type": "query", "params": [], "bindings": [1, "setup-page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": "view", "name": "frontend.partials.header", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.php", "line": 9}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.871245, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.header:9", "source": {"index": 12, "namespace": "view", "name": "frontend.partials.header", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.php", "line": 9}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fheader.blade.php&line=9", "ajax": false, "filename": "header.blade.php", "line": "9"}, "connection": "cardapp", "explain": null, "start_percent": 67.065, "width_percent": 9.898}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.883367, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1588", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1588", "ajax": false, "filename": "helpers.php", "line": "1588"}, "connection": "cardapp", "explain": null, "start_percent": 76.962, "width_percent": 9.386}, {"sql": "select * from `site_sections` where `key` = 'footer-section' limit 1", "type": "query", "params": [], "bindings": ["footer-section"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.885313, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.footer:5", "source": {"index": 15, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=5", "ajax": false, "filename": "footer.blade.php", "line": "5"}, "connection": "cardapp", "explain": null, "start_percent": 86.348, "width_percent": 6.826}, {"sql": "select * from `setup_pages` where `type` = 'useful-links' and `status` = 1 order by `id` asc", "type": "query", "params": [], "bindings": ["useful-links", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 7}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.8869948, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.footer:7", "source": {"index": 14, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=7", "ajax": false, "filename": "footer.blade.php", "line": "7"}, "connection": "cardapp", "explain": null, "start_percent": 93.174, "width_percent": 6.826}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\SiteSections": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSiteSections.php&line=1", "ajax": false, "filename": "SiteSections.php", "line": "?"}}, "App\\Models\\Admin\\BasicSettings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FBasicSettings.php&line=1", "ajax": false, "filename": "BasicSettings.php", "line": "?"}}, "App\\Models\\Admin\\SetupSeo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSetupSeo.php&line=1", "ajax": false, "filename": "SetupSeo.php", "line": "?"}}, "App\\Models\\Admin\\SetupPage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSetupPage.php&line=1", "ajax": false, "filename": "SetupPage.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-974303136 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-974303136\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1588935569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1588935569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1376965610 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1376965610\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1530958175 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjFPSEx6MnUxc3QyMjd5VTZYcmVid3c9PSIsInZhbHVlIjoicXRaRm5vZEJuTkIvRWJnSHI4UzVhUm9QUkZjUzVjYlNLdkF3WnZpVzRxV25waDczdTdUM1FxVlFMSWFzcm5NbnZpQzRSZmYyWFJpVGlnc2FZaW11NmVGNHhtWVBERUxOZkFQL0N4NVgwMzd0N2d0T3N0a3RYWG1RRXFZQTZKQWciLCJtYWMiOiIyNDdmOGRhZTM5M2MxYWExZTYxYTE0NTk4MzE3ZDRjZTAzODhmODAxY2UyZTU4MTc1MmEyZjIzMGE3MWNhYWJkIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6InpJNlRVK2Q2U2E3dU40bis5bHArVkE9PSIsInZhbHVlIjoia2VjcDZwMGxUcEZmeDJpNWtjKzR1aXZ3SkFtcG4wQkc5eGtFNEVwRkVLUDREcnYxRGRDWjlBUDdEd1NVamxxbHhyN1VoUEw5UVFxWjNWWkJtMTQxbkp2Q1Y5ZnZkalBzSkxGQitvaWZnTHJ4cVRVR1FiSDFzWFdXTHBWdVYrL0MiLCJtYWMiOiI5N2FiNGQ4Mjk5ZjFlNzIyM2RkYWJlNTk0NzJlYjQxYmI4M2MxYzdiOGQ5MTJkMTFjNjgxMGM2MWVlY2FjZjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530958175\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1359173581 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RygapEhuNHzIvKDgi3fvYLTxPvSb5kpnIslLDxGP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359173581\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2119522615 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 08:56:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVQdmhnUGVxOGJaN09iY0dzMW10Z3c9PSIsInZhbHVlIjoiK3lMYlRhZ3lvUVlIaGpMbU5jWEM3byt1YmZxZTF2VERjWThVd2F2aW9GMHpCbDJxc1ordENUUUg2Z0FjeVhFVG5WTFNiaDFPdHMwUWkyQWZJR3JJczB5emNyMWJvbHBzbDREbUlQcHdWaFdTSzJsZG5Tb2xSM0djbXVnSUF5NkMiLCJtYWMiOiI4OWJkNGNlNzMyYWU3YTViOTg2MmJjY2Y2MWFjZTIzZWNjOTJkZDU5NDk4YmEyNzMxMzQxN2VhMzIxZjBkM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:56:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IjZnNXJMcGlQbDBMclZsMTFadHdYTEE9PSIsInZhbHVlIjoib0FmSERRSjZkcTNUUHlQa1JRdmQ0R216TkJ4bkZJdVhDSUIrYnR1a1dKREdYR2hQQ0FWOXlUYmlsY09iVCtadExFK0RRRWt4OGRkRGVXMVlHa3V2TzFWUlE4RUFYQXJBWkZ0VFZ3SkxpRXdCZ2h1SitNZlJ2d0F6eUY3SVlmQksiLCJtYWMiOiI1ZTY5OGJiY2E2ODRiYzZmNGMzODMyNzhjMzliNGE3ZmI3ZDEzMDYyNjc3NDgwNWJjYmE0ZGI1YzFkNTA3NzM0IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:56:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVQdmhnUGVxOGJaN09iY0dzMW10Z3c9PSIsInZhbHVlIjoiK3lMYlRhZ3lvUVlIaGpMbU5jWEM3byt1YmZxZTF2VERjWThVd2F2aW9GMHpCbDJxc1ordENUUUg2Z0FjeVhFVG5WTFNiaDFPdHMwUWkyQWZJR3JJczB5emNyMWJvbHBzbDREbUlQcHdWaFdTSzJsZG5Tb2xSM0djbXVnSUF5NkMiLCJtYWMiOiI4OWJkNGNlNzMyYWU3YTViOTg2MmJjY2Y2MWFjZTIzZWNjOTJkZDU5NDk4YmEyNzMxMzQxN2VhMzIxZjBkM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:56:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IjZnNXJMcGlQbDBMclZsMTFadHdYTEE9PSIsInZhbHVlIjoib0FmSERRSjZkcTNUUHlQa1JRdmQ0R216TkJ4bkZJdVhDSUIrYnR1a1dKREdYR2hQQ0FWOXlUYmlsY09iVCtadExFK0RRRWt4OGRkRGVXMVlHa3V2TzFWUlE4RUFYQXJBWkZ0VFZ3SkxpRXdCZ2h1SitNZlJ2d0F6eUY3SVlmQksiLCJtYWMiOiI1ZTY5OGJiY2E2ODRiYzZmNGMzODMyNzhjMzliNGE3ZmI3ZDEzMDYyNjc3NDgwNWJjYmE0ZGI1YzFkNTA3NzM0IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:56:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119522615\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}