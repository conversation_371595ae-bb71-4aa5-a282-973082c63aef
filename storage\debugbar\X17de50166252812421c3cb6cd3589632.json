{"__meta": {"id": "X17de50166252812421c3cb6cd3589632", "datetime": "2025-06-23 15:36:24", "utime": **********.384059, "method": "GET", "uri": "/pusher/beams-auth?user_id=127-0-0-1-admin-1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.114177, "end": **********.384076, "duration": 0.2698991298675537, "duration_str": "270ms", "measures": [{"label": "Booting", "start": **********.114177, "relative_start": 0, "end": **********.332386, "relative_end": **********.332386, "duration": 0.21820902824401855, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332396, "relative_start": 0.21821904182434082, "end": **********.384078, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "51.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pusher/beams-auth", "middleware": "web, auth:admin, app.mode, admin.role.guard", "uses": "Closure(Request $request) {#429\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#428 …}\n  file: \"D:\\projects\\CardApp\\routes\\admin.php\"\n  line: \"373 to 409\"\n}", "namespace": null, "prefix": "", "where": [], "as": "pusher.beams.auth", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Froutes%2Fadmin.php&line=373\" onclick=\"\">routes/admin.php:373-409</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00215, "accumulated_duration_str": "2.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.3576849, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 28.372}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.3610122, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 28.372, "width_percent": 20.93}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.363358, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 49.302, "width_percent": 16.279}, {"sql": "select * from `admin_role_permissions` where `admin_role_permissions`.`admin_role_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.364744, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 65.581, "width_percent": 16.279}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.366971, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 81.86, "width_percent": 18.14}]}, "models": {"data": {"App\\Models\\Admin\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Admin\\AdminHasRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminHasRole.php&line=1", "ajax": false, "filename": "AdminHasRole.php", "line": "?"}}, "App\\Models\\Admin\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pusher/beams-auth?user_id=127-0-0-1-admin-1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750659774\n]", "local": "ar"}, "request": {"path_info": "/pusher/beams-auth", "status_code": "<pre class=sf-dump id=sf-dump-1166463330 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1166463330\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2059909748 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"17 characters\">127-0-0-1-admin-1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059909748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1981710764 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981710764\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1606612027 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/profile/index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6IitOM0pWS2E3SWh2WDkrak1xK01JTGc9PSIsInZhbHVlIjoiSm9xbC9TS0ZsMnpmWEZWcTU4Qi9YazlvWUc2R3NQZU9KU0lzYThvQTg4MVJOeWdvVFVjTGJYSVcvdng1REZxTyIsIm1hYyI6Ijg1YzVjNTczNDliMjExMmJiMzY1OTk0MzRiMTEyZDhkODMwZmVhNzk5NDAwOTVmMWJiMWJhYjJhNjEwMjBhYTQiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6IkUvdnhuRXdFbU1rODJuOHBadmF5TkE9PSIsInZhbHVlIjoiV2pXMHY4MWpORCs3L0hoNEVJMTFNazd2TW5nTjBvVlNnOG93aFBDVEpLdkhuT2ZReGlXMzdqbmh6cDlzRzgvS004aTk5eEZSL2kwRVZ5VHVJZjNvRkdlc3kxNWZFODRlNzI4TWdLakdRMjVxK2NRSk5KcU9MYVhMaXA5djJpYXd4SWJLeXhPdDJQYmtRK2tEMGtadS9FYWhSNzdBendPRC9lYmFRZ1kvUC9ZaTQvYkg0TnZHTG1hbjRGdXBoMFNGL2kvVEs4a0luNGVkT0NkdmlYV2NSUT09IiwibWFjIjoiNDc0MDlkNDUyMGJkYjBkOWE3YmI2MWU3NmE4ZGQ2OGM2NTg5YzI3ZTMxYTIxNDU2MDMzOTBmOWZiMDg0NGEyZCIsInRhZyI6IiJ9; ip_address=eyJpdiI6IllSZnB5R245MHlnejNNb0Z6MENMWEE9PSIsInZhbHVlIjoicmkwekJvY0F0MVVqT1VGV2VObkF1blFGdHZrbHhYOEFHTjVIOFp2UHJqUnQwdkFHVjhpQ3BBMGlZL0owK1liZ1huZzIwY3pQOXFvNXpoUko1M3RTb3c9PSIsIm1hYyI6Ijg4NjBmOTUxZjdjZThhY2VjYTU0Mzg3ZDc0N2YzNTAzYWIxNDRkZjkzNzJmMDNkMmQ3NzUwZTI4NmQwMDVlM2EiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjZkamlpRmFaQlhuVmQ3VjBjSDVWakE9PSIsInZhbHVlIjoidUV6YmZRQ1UwYUNDUDZJVndkY0p0djRRSzJVRVZOd2lNcTE4cFluZmVlWGplTXR5cWN0Y0JReHNsN3BFa1lZTSIsIm1hYyI6ImQ4OTI1ODQxMWM1ODliN2JmMWYzOGExNGZhOTkyYTc1ZmQxNDQyNzA0OTkwYjkzMGIwMmFjN2U3MWI1ZWIxOWIiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IkVCdkR1QjB5c2RDdnZJZHZUQStLYWc9PSIsInZhbHVlIjoiSlFlZ2FYaVJDUkxucU9ERjBJL3VUWjZ6QWJJNkFFWnRLNWxuQ0dkVEpJZllTVUZDc0VvY2VlL0JyQTFVNmNTa2JPWlpKa0NLbFFCbVZTN0FFUFNjZlE9PSIsIm1hYyI6IjE0ZjBhMTI5YTdlZjFiMGQxN2MwY2E5NjlkNjYyYjU2ZGZiYmJhZDI2ZDBlNWM1NGQ4MTVmM2ExODg0ZDM5ZGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6ImdpNW1ZbTlLc044bko2VEhKRFRHWkE9PSIsInZhbHVlIjoibVhxcGJyK3FHaGVaQVcvZmJjZ25OUFFUWmkvKzhUNDNDOGJhVDVJMlV0T2M5U000SkpmbjNJSUFxc1dCdzYycHdZVTFXL2s2TWN3QU14cjZUQlUvU2R0aGlTakowUUViOTVyU1FJR2RMU0MxOHpiOWpZdUt6bmJnMkd5dUVYbHQiLCJtYWMiOiI3MDA4OGI2MTAxZTc4ODRjZTNiNWM0ZjFhM2M0ODY0MzljZTNmNTNjNGEzOTQ3MzNlYjQyMWI2ZjcyNTljYjBiIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6InJVTnc0TEVmVFEvdUFBbEdBczdSQWc9PSIsInZhbHVlIjoib2FKWTBmQmt2bjRlRmVkMkhCVU9ERzV6K2VTcWt0WERCWGRBUTM3MzhqUmgvcXJ3UmwyZndDRVdGelRCZTZsWWYveUVKSnoybG9uc1JGa2xtbjh6Tm1HMXZjelRST2tQVFVaY0hSbmovaHM5bEEzNHBiVUQxcklkSnJsUkI5cTUiLCJtYWMiOiI3ODgxM2FmYjQ4YjQyZTVlZTRlNTk3MTdmZjJlOTI0ZjMwYTBkNDU1OTE4N2JjMzE0M2VhODU4Y2IxYjkyNjNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606612027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1624735104 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TckR1LWzJcECAEEV9U5D46L3vYjTeYlIxc6zCKj5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1624735104\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-808720739 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 09:36:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJqcFcvUW10MEowRnE4alByRzlEK2c9PSIsInZhbHVlIjoic0pSVVR5VWpkRVdDSEloU1JPcFpuYWNacEFaZEVZL05KS2FYRHozMnNLaW91VDh6U2FOZTl6Wks0ekRpSXRhUk5Mdy9SYmNQeTFqTmZLS3YzTzkrZnlkYlNRNDVHeEI5V2VqWk5Yb3hDK2VkSTFRcDdEanlnR0pTTDZzT0haN3kiLCJtYWMiOiI0NTZjZGNlZWQ5ZGIyNmNlN2Q0Y2YxYmE3NTYwMzIyNWQxOWE2YWZlODM4ODdjMmNiYTZhMThkYjg4NmExYmM5IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6ImxFb0tDNG9vSVpyQjNFSWtJMm8vMFE9PSIsInZhbHVlIjoiZFVPUkdBSWhXb3MzdjBJdExqQmsvWkFTMit0cUNtbWF5YnNlaGg4TjdPcnh1RmQ0K2FmRGh5UkU0VFM3bkJyQUdUcnZ0emduYXhnYmdVeW1xVDh1ZWhOaWpyRm8ySllmMGVsQ2RqNVNJVG5DeTFMQVNJMHc2SDBqdDBacWxudzQiLCJtYWMiOiIwN2NiMGM0NjY0MDJhY2IwNzI4MGVlYThlODcwMTY0ZDkxZGQ3N2VlOTI3NGU3YWNlMjcyNTNmMjExMjhiYjJhIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJqcFcvUW10MEowRnE4alByRzlEK2c9PSIsInZhbHVlIjoic0pSVVR5VWpkRVdDSEloU1JPcFpuYWNacEFaZEVZL05KS2FYRHozMnNLaW91VDh6U2FOZTl6Wks0ekRpSXRhUk5Mdy9SYmNQeTFqTmZLS3YzTzkrZnlkYlNRNDVHeEI5V2VqWk5Yb3hDK2VkSTFRcDdEanlnR0pTTDZzT0haN3kiLCJtYWMiOiI0NTZjZGNlZWQ5ZGIyNmNlN2Q0Y2YxYmE3NTYwMzIyNWQxOWE2YWZlODM4ODdjMmNiYTZhMThkYjg4NmExYmM5IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6ImxFb0tDNG9vSVpyQjNFSWtJMm8vMFE9PSIsInZhbHVlIjoiZFVPUkdBSWhXb3MzdjBJdExqQmsvWkFTMit0cUNtbWF5YnNlaGg4TjdPcnh1RmQ0K2FmRGh5UkU0VFM3bkJyQUdUcnZ0emduYXhnYmdVeW1xVDh1ZWhOaWpyRm8ySllmMGVsQ2RqNVNJVG5DeTFMQVNJMHc2SDBqdDBacWxudzQiLCJtYWMiOiIwN2NiMGM0NjY0MDJhY2IwNzI4MGVlYThlODcwMTY0ZDkxZGQ3N2VlOTI3NGU3YWNlMjcyNTNmMjExMjhiYjJhIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:36:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808720739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1394700512 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">http://127.0.0.1:8000/pusher/beams-auth?user_id=127-0-0-1-admin-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750659774</span>\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394700512\", {\"maxDepth\":0})</script>\n"}}