@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/custom/money-send.css') }}" />
<link
rel="stylesheet"
href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css"
integrity="sha384-gXt9imSW0VcJVHezoNQsP+TNrjYXoGcrqBZJpry9zJt8PCQjobwmhMGaDHTASo9N"
crossorigin="anonymous"
/>
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __(@$page_title)])
@endsection

@section('content')
<div>
<section class="mony_send row border__primary">
    <form id='sendMoneyForm' class="rounded-3 col-md-6" method="POST" action="{{ route('user.transfer.money.confirmed') }}">
      @csrf
      <p class="text-start">{{ __('Send Money') }}</p>
      <div class="bg--gray p-3">
        <div class="text-center d-flex flex-column w-100 gap-3 mb-1">
          <div class="text-start">
            <label for="email">{{ __('Email') }}</label>
            <input
              required 
              name="email"
              type="text"
              class="form-control @error('email') is-invalid @enderror"
              id="email"
              placeholder='{{ __("Enter recipient email address") }}'
              value="{{ old('email') }}"
            />
            <span class='text-danger bg-transparent req__email' style='font-size:12px;'>{{ __('required') }}</span>
            @error('email')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="text-start transaction_form">
            <label for="transaction">{{ __('Transfer Amount') }}</label>
            <input
            required 
              name="amount"
              type="number"
              class="form-control @error('amount') is-invalid @enderror"
              id="transaction"
              placeholder='{{ __("Enter Amount") }}'
              value="{{ old('amount') }}"
            />
            <span class='text-danger bg-transparent req__amount' style='font-size:12px;'>{{ __('required') }}</span>
            <span class='text-danger bg-transparent totalAmountReqMsg' style='font-size:12px;'>{{ __('must bes less than max limit') }}</span>
            @error('amount')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

        <div class="d-flex w-100 justify-content-between py-1 my-2">
          <span class="text--primary balance__text" id='availableBalance'>{{ __('Available Balance') }} {{ $user_wallet->balance }} USD</span>
        </div>
        <div class="d-flex w-100 justify-content-between py-1 mb-3">
          <span class="text-secondary balance__text">  <span>{{ __('Charge') }}</span> <span dir="rtl"> <span>USD</span>  <span>{{ $transferMoneyCharge->fixed_charge }}</span> + <span>{{ $transferMoneyCharge->percent_charge }}</span>% </span> </span>
          <span class="text-secondary balance__text">  <span>{{ __('Limit') }}</span> <span dir="rtl"> <span>USD</span>  <span>{{ $transferMoneyCharge->min_limit }}</span> - <span>USD</span> <span id='max_limit'>{{ $transferMoneyCharge->max_limit }}</span> </span> </span>

        </div>
        <button id="confirm-btn" type="button" class="confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold">
          {{ __('Confirm') }}
        </button>
      </div>
    </form>
    <div class="col-md-6">
        <p class="text-start">{{ __('Send Money Preview') }}</p>
        <div class="bg--gray p-3 rounded-3 d-flex flex-column gap-3">
            <div class="d-flex align-items-center justify-content-between send__money__details">
                <span>{{ __('Entered Amount') }}</span>
                <span id="entered-amount">0 USD</span>
            </div>
            <div class="d-flex align-items-center justify-content-between">
                <span>{{ __('Transfer Fees') }}</span>
                <span id="transfer-fees">0 USD</span>
            </div>
            <div class="d-flex align-items-center justify-content-between">
                <span>{{ __('Recipient Will Receive') }}</span>
                <span id="you-will-receive">0 USD</span>
            </div>
            <div class="d-flex align-items-center justify-content-between">
                <span>{{ __('Total Amount Due') }}</span>
                <span id="total-amount-due">0 USD</span>
            </div>
        </div>
    </div>

  </section>

  <!-- transaction history section start -->
  <section class="transactions d-flex flex-column mt-4">
    <div class="transactions__header">
      <p>{{ __('Send Money History') }}</p>
    </div>
    <ul class="timeline w-100">
      @include('user.components.transaction-log',compact("transactions"))
    </ul>
  </section>
</div>

 <!-- Modal Start -->
 <div
        class="modal fade"
        id="confirmation"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-labelledby="sendMoneyLabel"
        aria-hidden="true"
        style="z-index: 9999"
    >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmationLabel">{{ __('Send Money') }}</h5>
                        <button
                            type="button"
                            class="btn-close bg-transparent"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                        >
                            <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
                        </button>
                    </div>
                    <div class="modal-body">
                        <h6 class="text-dark">
                            {{ __('Are you sure you want to send') }}
                            <span class="text--primary" id="modal-amount"></span> USD {{ __('to the email') }}
                            <span class="text--primary" id="modal-email"></span> ?
                        </h6>
                    </div>
                    <div class="modal-footer d-flex justify-content-center">
                        <button type="button" id='postFormValues' class="btn primary__btn__base">{{ __('Yes') }}</button>
                        <button type="button" data-bs-dismiss="modal" aria-label="Close" class="btn secondary__btn__base">{{ __('No, go back') }}</button>
                    </div>
            </div>
        </div>
    </div>
@endsection


@push('script')
<script>
  let totalAmountDue = 0
  document.getElementById('transaction').addEventListener('input', function() {
    
    var amount = parseFloat(this.value) || 0;
  
    // Assuming the values for fixed charge and percent charge are available
    var fixedCharge = {{ $transferMoneyCharge->fixed_charge }};
    var percentCharge = {{ $transferMoneyCharge->percent_charge }};
  
    // Calculate charges
    var totalPercentCharge = (amount * percentCharge) / 100;
    var totalCharge = fixedCharge + totalPercentCharge;
  
    // Calculate the total payable and the amount received
    totalAmountDue = amount + totalCharge;
    var youWillReceive = amount;
    // Update the summary fields
    document.getElementById('entered-amount').textContent = amount.toFixed(2) + " USD";
    document.getElementById('transfer-fees').textContent = totalCharge.toFixed(2) + " USD";
    document.getElementById('you-will-receive').textContent = youWillReceive.toFixed(2) + " USD";
    document.getElementById('total-amount-due').textContent = totalAmountDue.toFixed(2) + " USD";
});

//  ? form submission logic
const confirmButton = document.getElementById('confirm-btn');
const postFormBtn = document.getElementById('postFormValues');
const form = document.getElementById('sendMoneyForm');
const emailInput = document.getElementById('email');
const transactionInput = document.getElementById('transaction');
const modalAmount = document.getElementById('modal-amount');
const max_limit = document.getElementById('max_limit');
const modalEmail = document.getElementById('modal-email');
const emailReqMsg = document.querySelector('.req__email')
const amountReqMsg = document.querySelector('.req__amount')
const totalAmountReqMsg = document.querySelector('.totalAmountReqMsg')
const availableBalance = document.getElementById('availableBalance');
const maxLimitValue = +max_limit.innerText
const availableBalanceValue = +availableBalance.innerText.match(/\d+/)[0]; 

  emailReqMsg.style.display = 'none'
  amountReqMsg.style.display = 'none'
  totalAmountReqMsg.style.display = 'none'


 emailInput.addEventListener('input',(e)=>{
        if(!!e.target.value){
            emailReqMsg.style.display = 'none'
        }else{
            emailReqMsg.style.display = 'block'
        }
    })
    // ? address input
    transactionInput.addEventListener('input',(e)=>{
      if(maxLimitValue < e.target.value){
        totalAmountReqMsg.style.display = 'block'
      }
      else if(!!e.target.value || maxLimitValue > e.target.value){
        amountReqMsg.style.display = 'none'
        totalAmountReqMsg.style.display = 'none'
      }else{
        amountReqMsg.style.display = 'block'
        }
    })

    confirmButton.addEventListener('click', function() {
    totalAmountReqMsg.style.display = 'none';
    emailReqMsg.style.display = 'none';
    amountReqMsg.style.display = 'none';

    // Validate total amount
    if (availableBalanceValue < totalAmountDue) {
        totalAmountReqMsg.style.display = 'block';
        return;
    }

    // Validate email input
    let isEmailValid = !!emailInput.value;
    if (!isEmailValid) {
        emailReqMsg.style.display = 'block';
    }

    // Validate transaction input
    let isTransactionValid = !!transactionInput.value;
    if (!isTransactionValid) {
        amountReqMsg.style.display = 'block';
    }

    // Show confirmation modal if all inputs are valid
    if (isEmailValid && isTransactionValid) {
        modalAmount.innerHTML = totalAmountDue;
        modalEmail.innerHTML = emailInput.value;
        $('#confirmation').modal('show');
    }
});


  postFormBtn.addEventListener('click', function() {
  form.submit()
  });




</script>
<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>
@endpush
