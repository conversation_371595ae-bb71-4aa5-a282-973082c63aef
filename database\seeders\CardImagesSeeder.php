<?php

namespace Database\Seeders;

use App\Models\CardImage;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class CardImagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get cards from the external service
        $cardsResponse = app('App\Http\Controllers\User\MyCardsController')->getcards(request());

        // Decode the JSON response if it's a JsonResponse object
        if ($cardsResponse instanceof \Illuminate\Http\JsonResponse) {
            $cardsResponse = $cardsResponse->getData(true); // Decode it into an array
        }

        // Fetch all card images from the specified directory
        $cardImageFiles = File::files(public_path('frontend/assets/cards'));

        // Extract the image paths from the file objects
        $imagePaths = array_map(function ($file) {
            return '/frontend/assets/cards/' . $file->getFilename();
        }, $cardImageFiles);

        // Loop through each card in the response
        foreach ($cardsResponse['data'] as $card) {
            // Get the BIN from the card details (use first 8 digits)
            $bin = substr($card['bin'], 0, 8); 

            // Find a matching card image by bin or assign a random image from the directory
            $cardImage = CardImage::where('bin', $bin)->first();
            $imagePath = $cardImage ? $cardImage->image_path : $imagePaths[array_rand($imagePaths)];

            // Insert the BIN and corresponding background image into the card_images table
            DB::table('card_images')->updateOrInsert([
                'bin' => $bin,
            ], [
                'image_path' => $imagePath,
            ]);
        }
    }
    // Function to map BINs to image paths
    private function getImageForBin($bin)
    {
        // You can assign image paths based on some condition or mapping
        $imageMappings = [
            '123456' => 'frontend/assets/vcards/card1.svg',
            '654321' => 'frontend/assets/vcards/card2.svg',
            '789012' => 'frontend/assets/vcards/card3.svg',
            '210987' => 'frontend/assets/vcards/card4.svg',
        ];

        // Default image if no specific mapping exists
        return $imageMappings[$bin] ?? 'frontend/assets/vcards/default_card.svg';
    }
}
