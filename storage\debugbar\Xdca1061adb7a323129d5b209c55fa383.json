{"__meta": {"id": "Xdca1061adb7a323129d5b209c55fa383", "datetime": "2025-06-22 15:41:31", "utime": 1750585291.003163, "method": "POST", "uri": "/user/logout", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.689454, "end": 1750585291.003193, "duration": 0.3137388229370117, "duration_str": "314ms", "measures": [{"label": "Booting", "start": **********.689454, "relative_start": 0, "end": **********.959596, "relative_end": **********.959596, "duration": 0.2701418399810791, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.959608, "relative_start": 0.2701539993286133, "end": 1750585291.003196, "relative_end": 3.0994415283203125e-06, "duration": 0.04358792304992676, "duration_str": "43.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24492744, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST user/logout", "middleware": "web, auth, verification.guard", "controller": "App\\Http\\Controllers\\User\\DashboardController@logout", "as": "user.logout", "namespace": null, "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=45\" onclick=\"\">app/Http/Controllers/User/DashboardController.php:45-50</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00172, "accumulated_duration_str": "1.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `virtual_card_apis` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 881}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 706}], "start": **********.974619, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:20", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=20", "ajax": false, "filename": "DashboardController.php", "line": "20"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 30.233}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.986063, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 30.233, "width_percent": 42.442}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.989172, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 72.674, "width_percent": 27.326}]}, "models": {"data": {"App\\Models\\VirtualCardApi": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FVirtualCardApi.php&line=1", "ajax": false, "filename": "VirtualCardApi.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9XpKONhg6R7EkPfIT3DvuZMNTD3Rms5TEjlsTpNS", "success": "array:1 [\n  0 => \"Logout Successfully!\"\n]", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/logout", "status_code": "<pre class=sf-dump id=sf-dump-839395262 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-839395262\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-812032888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-812032888\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-740451797 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YD8RZr6uzyLFLhi4cVqSaiJVLcROX7z7unyrscKd</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740451797\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-99221100 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">69</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Ii9hMkJHUjJqWkxmRlpPSyt6OFJiR2c9PSIsInZhbHVlIjoiSjJVRGlKNWllSXZQa3lhVFpQVklONlArQVdrS0V0ZTkrUHBWdE5OcUZHQjZZSURoclNQbHhHblp5NnhNNzcycFNuMFBOMHJOcnVPVjlJZEpnTldZN0ZBNSt6cmdwU1BzZCs1WDZSeHJjdXp4Zk5ndnBqbVZFR1RPajZHSmtmaVMiLCJtYWMiOiIzYjZmYzJlYTI2ODY2Yzk4OGFkZjMzNTdmMTI5NzhiMGMzZTgyMTNiZGExNDUxZWM1MWY2NWY3YjNlNTNhMjczIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6ImhqRkxHWTN6L25pcDBZNGQwQlorWFE9PSIsInZhbHVlIjoiZ2JjdHRRZ1ZUa0VRaEx5dWR6MmdHODhUMTU2bERRcmlhRjZVVjEwMjIxV0VZQnAvT0s0OUhEM2RvYjBkSFBocys4WC9udS9oelFiWG9xRlhQZEtvUmloVXB3QUw4Sk1ZREtDbjh4cERIYTNQUGRVQStPMVJsRC9FdGkvTXpFY1oiLCJtYWMiOiI2MzhlOWQwY2Y0N2MyNjNmMzEzODA4MDM3MzFkZGZiMmQyYzA3OTBmOGFjZTkzMmM3ZmIzNDUwOGU3MWMwYmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99221100\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1247601732 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YD8RZr6uzyLFLhi4cVqSaiJVLcROX7z7unyrscKd</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IRGK5Vxvyfu0ok6VojJBUVZ4V37UFco7Xl6Towha</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247601732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2062050816 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:41:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFFTHlKbHdzUXdRcitRYnJWbCtGR1E9PSIsInZhbHVlIjoibzREeHBHK2RDMElyaDl5bzdrblcwQWduR2NRWDBBdmhaUldKQnJWRGgwRlpITVVKNU02SG1kS2R6SXhiaC9wd053ZGZXd1BEREJZeUJ2S0FwU1g5dW9ZZCs2bzF4K0svSlV0eFVFVWVrTDhVRGFWVnRKSE1URWtJUVhObmpSN2MiLCJtYWMiOiIxOWIzYjY0M2VlNDQyZmFmZjg4MTQ3YWZhNDBmMjAwNWYxMTc4YzM1OGY2NTEwODBkY2RkYzI0NTlhYzA3OTU0IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:41:30 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6Ikl5ZHp1R3VPcFhGY29pc3R5KytKNEE9PSIsInZhbHVlIjoiZmtzS1hXbmUzUStPcW9WSXVBclY3bDFMRzQvU3hTVEx2RkJ1dHpWa1BFOGtUSFVGdXVxUnZpVU8yOGZQRFhiaFFqQWgvZndrdEZHVHpubE1YNmgxNnhRYXA3dGVuYnhOWEUrN3crTWhzQ3M0RysvUjJ3SksrWm01dCszMWRVaUkiLCJtYWMiOiI1ZTg1NTFiZGYxYjgxOGVmZDAwNjJjMWFiMTRmZTE2MjE3Yjk3NTE1YzFhNjRmMTg0MWM4ZTU1NTYyMThjMzU4IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:41:30 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFFTHlKbHdzUXdRcitRYnJWbCtGR1E9PSIsInZhbHVlIjoibzREeHBHK2RDMElyaDl5bzdrblcwQWduR2NRWDBBdmhaUldKQnJWRGgwRlpITVVKNU02SG1kS2R6SXhiaC9wd053ZGZXd1BEREJZeUJ2S0FwU1g5dW9ZZCs2bzF4K0svSlV0eFVFVWVrTDhVRGFWVnRKSE1URWtJUVhObmpSN2MiLCJtYWMiOiIxOWIzYjY0M2VlNDQyZmFmZjg4MTQ3YWZhNDBmMjAwNWYxMTc4YzM1OGY2NTEwODBkY2RkYzI0NTlhYzA3OTU0IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:41:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6Ikl5ZHp1R3VPcFhGY29pc3R5KytKNEE9PSIsInZhbHVlIjoiZmtzS1hXbmUzUStPcW9WSXVBclY3bDFMRzQvU3hTVEx2RkJ1dHpWa1BFOGtUSFVGdXVxUnZpVU8yOGZQRFhiaFFqQWgvZndrdEZHVHpubE1YNmgxNnhRYXA3dGVuYnhOWEUrN3crTWhzQ3M0RysvUjJ3SksrWm01dCszMWRVaUkiLCJtYWMiOiI1ZTg1NTFiZGYxYjgxOGVmZDAwNjJjMWFiMTRmZTE2MjE3Yjk3NTE1YzFhNjRmMTg0MWM4ZTU1NTYyMThjMzU4IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:41:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062050816\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1759488233 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9XpKONhg6R7EkPfIT3DvuZMNTD3Rms5TEjlsTpNS</span>\"\n  \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Logout Successfully!</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759488233\", {\"maxDepth\":0})</script>\n"}}