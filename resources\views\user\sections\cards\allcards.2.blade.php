@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/all_created_cards.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')
<div class="cards">
<main>
  <h3 class="text-start">البطاقات الإفتراضية</h3>
  <div class="border__primary images_wrapper row gap-4">
    <!-- Loop through each card and display the data -->
    @foreach($cards as $card)
    <div class="img__wrapper col-lg-3 col-md-6">
      <img src="{{ asset('frontend/assets/icons/credit-card.svg') }}" alt="card" />

      @php
        $cardTypeImage = $card->type == 'Visa' ? 'visa.svg' : 'mastercard.svg';
      @endphp

      <img src="{{ asset('frontend/assets/icons/' . $cardTypeImage) }}" alt="{{ $card->type }}" class="card_type" />
      <p class="card_id">{{ $card->bin }}</p>
      {{-- <p class="card_owner">{{ $card->card_owner }}</p> --}}
      {{-- <p class="card_expiration">Exp {{ $card->expiration_date }}</p> --}}
      <button
        class="primary__btn__base primary__btn"
        type="button"
        data-bs-toggle="modal"
        data-bs-target="#addToCardsModal{{ $card->id }}"
      >
        ضفها الى بطاقاتي
      </button>
    </div>

    <!-- modal for each card -->
    <div
      class="modal fade"
      id="addToCardsModal{{ $card->id }}"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="addToCardsModalLabel{{ $card->id }}"
      aria-hidden="true"
      style="z-index: 9999;"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <form action="{{ route('user.cards.add_new_card') }}" method="POST">
            @csrf
            <div class="modal-header">
              <h5 class="modal-title" id="addToCardsModalLabel{{ $card->id }}">
                اختر البطاقة
              </h5>
              <input type="hidden" name="bin" value="{{ $card->bin }}">
              <button
                type="button"
                class="btn-close bg-transparent"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
              </button>
            </div>
            <div class="modal-body">
              <h5 class="text-dark">
                هل أنت متأكد من رغبتك في امتلاك هذة البطاقة الإفتراضية؟
              </h5>
            </div>
            <div class="modal-footer d-flex justify-content-center">
              <button type="submit" class="btn btn--base">نعم اضفها لبطاقاتي</button>
              <button type="button" class="btn secondary__btn__base" data-bs-dismiss="modal"
              aria-label="Close">لا, العودة</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!-- end of modal -->
    @endforeach
  </div>
</main>
</div>
@endsection

@push('script')
<script src="{{ asset('frontend/js/custom/allCreatedCards.js') }}"></script>
@endpush
