<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\VirtualCard;
use App\Models\VirtualCardApi;
use App\Models\VirtualCardSetting;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class VirtualCardController extends Controller
{
    public function cardApi()
    {
        $page_title = __("Setup Virtual Card Api");
        $api = VirtualCardSetting::first();
        return view('admin.sections.virtual-card.api',compact(
            'page_title',
            'api',
        ));
    }
    public function cardApiUpdate(Request $request){
        $validator = Validator::make($request->all(), [
            'secret_key' => 'required|string',
            'max_limit' => 'required|numeric',
            'fixed_creation_fee' => 'required|numeric',
            'percent_creation_fee' => 'required|numeric',
            'fixed_purchase_fee' => 'required|numeric',
            'percent_purchase_fee' => 'required|numeric',
            'fixed_withdraw_fee' => 'required|numeric',
            'percent_withdraw_fee' => 'required|numeric',
            'fixed_recharge_fee' => 'required|numeric',
            'percent_recharge_fee' => 'required|numeric',
            'account_uuid' => 'required|string',
            'allow_adv_cards' => 'required|boolean',
            'monthly_fees' => 'required|numeric',
            'image' => 'nullable|mimes:png,jpg,jpeg,webp,svg',
        ]);
    
        if($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
    
        $data = $request->except('_token', '_method', 'image');
        $api = VirtualCardSetting::first();
    
        // Update the model instance with the validated data
        $api->fill($data);
    
        if ($request->hasFile("image")) {
            try {
                $image = get_files_from_fileholder($request, "image");
                $upload_file = upload_files_from_path_dynamic($image, "card-api");
                $api->image = $upload_file;
            } catch (Exception $e) {
                return back()->with(['error' => [__('Ops! Failed To Upload Image.')]]);
            }
        }
    
        $api->save();
    
        return back()->with(['success' => [__('Card API Has Been Updated.')]]);
    }
    

    public function transactionLogs()
    {
        $page_title = __("Virtual Card Logs");
        $transactions = Transaction::with(
          'user:id,firstname,lastname,email,username,full_mobile',
            'currency:id,name',
        )->where('type', 'VIRTUAL-CARD')->latest()->paginate(20);

        return view('admin.sections.virtual-card.logs', compact(
            'page_title',
            'transactions'
        ));
    }
}
