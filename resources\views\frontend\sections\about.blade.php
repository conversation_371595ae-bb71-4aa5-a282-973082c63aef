@php
    $lang = selectedLang();
    $about_slug = Illuminate\Support\Str::slug(App\Constants\SiteSectionConst::ABOUT_SECTION);
    $about_sections = App\Models\Admin\SiteSections::getData($about_slug)->first();
@endphp

<section>
    <div class="about bg-gray p-5">
      <div class="container d-flex align-items-center justify-content-center">
        <div class="row d-flex gap-lg-0 gap-3">
          <div class="col-md-6">
            <div class="frame">
              <img
                height="538"
                width="600"
                src="{{ asset('frontend/assets/website_about_img.svg') }}"
                alt="card"
              />
            </div>
          </div>
          <div
            class="heading col-md-6 d-flex flex-column gap-lg-3 align-items-md-start align-items-center gap-4"
          >
            <span class="chip">{{ __('about_us') }}</span>
            <h3>{{ __('secure_online_solutions') }}</h3>
            <p class="mt-1">
              {{ __('card_app_virtual_cards') }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="p-5">
      <div class="container d-flex align-items-center justify-content-center">
        <div class="row d-flex gap-lg-0 gap-3">
          <div
            class="heading col-md-6 d-flex flex-column gap-lg-3 align-items-md-start align-items-center gap-4"
          >
            <h3>{{ __('secure_online_solutions') }}</h3>
            <p class="mt-1">
              {{ __('card_app_virtual_cards') }}
            </p>
          </div>
          <div class="col-md-6">
            <div class="frame">
              <img
                height="538"
                width="600"
                src="{{ asset('frontend/assets/visual_visa.png') }}"
                alt="card"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
</section>

@push('css')
  <style>
      .chip {
          background-color: rgba(99, 91, 254, 0.05);
          padding: 5px 12px;
          border-radius: 36px;
          color: var(--primary-color);
          width: fit-content;
      }
      .bg-gray {
          background-color: #F5F7F9;
      }

      .heading h3 {
          color: #2A3342;
          font-size: 48px;
          font-weight: 700;
      }

      .heading p {
          color: #556987;
          font-size: 20px;
          font-weight: 500;
      }
  </style>
@endpush
