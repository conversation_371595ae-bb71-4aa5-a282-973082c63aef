{"__meta": {"id": "Xf4d6e1f53456151a2f85023de443e125", "datetime": "2025-06-23 15:24:09", "utime": **********.236013, "method": "GET", "uri": "/pusher/beams-auth?user_id=127-0-0-1-admin-1", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750670648.954059, "end": **********.236045, "duration": 0.2819859981536865, "duration_str": "282ms", "measures": [{"label": "Booting", "start": 1750670648.954059, "relative_start": 0, "end": **********.182745, "relative_end": **********.182745, "duration": 0.22868609428405762, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.182758, "relative_start": 0.2286992073059082, "end": **********.236047, "relative_end": 2.1457672119140625e-06, "duration": 0.053288936614990234, "duration_str": "53.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pusher/beams-auth", "middleware": "web, auth:admin, app.mode, admin.role.guard", "uses": "Closure(Request $request) {#429\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#428 …}\n  file: \"D:\\projects\\CardApp\\routes\\admin.php\"\n  line: \"373 to 409\"\n}", "namespace": null, "prefix": "", "where": [], "as": "pusher.beams.auth", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Froutes%2Fadmin.php&line=373\" onclick=\"\">routes/admin.php:373-409</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00246, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.20706, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 26.016}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.21047, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 26.016, "width_percent": 17.48}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.21267, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 43.496, "width_percent": 15.041}, {"sql": "select * from `admin_role_permissions` where `admin_role_permissions`.`admin_role_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 28, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 29, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.214256, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 58.537, "width_percent": 21.545}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.217314, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 80.081, "width_percent": 19.919}]}, "models": {"data": {"App\\Models\\Admin\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Admin\\AdminHasRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminHasRole.php&line=1", "ajax": false, "filename": "AdminHasRole.php", "line": "?"}}, "App\\Models\\Admin\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pusher/beams-auth?user_id=127-0-0-1-admin-1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750659774\n]", "local": "ar"}, "request": {"path_info": "/pusher/beams-auth", "status_code": "<pre class=sf-dump id=sf-dump-597220533 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-597220533\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2038328978 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"17 characters\">127-0-0-1-admin-1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038328978\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-904634325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-904634325\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2071944681 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/profile/index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6IitOM0pWS2E3SWh2WDkrak1xK01JTGc9PSIsInZhbHVlIjoiSm9xbC9TS0ZsMnpmWEZWcTU4Qi9YazlvWUc2R3NQZU9KU0lzYThvQTg4MVJOeWdvVFVjTGJYSVcvdng1REZxTyIsIm1hYyI6Ijg1YzVjNTczNDliMjExMmJiMzY1OTk0MzRiMTEyZDhkODMwZmVhNzk5NDAwOTVmMWJiMWJhYjJhNjEwMjBhYTQiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6IkUvdnhuRXdFbU1rODJuOHBadmF5TkE9PSIsInZhbHVlIjoiV2pXMHY4MWpORCs3L0hoNEVJMTFNazd2TW5nTjBvVlNnOG93aFBDVEpLdkhuT2ZReGlXMzdqbmh6cDlzRzgvS004aTk5eEZSL2kwRVZ5VHVJZjNvRkdlc3kxNWZFODRlNzI4TWdLakdRMjVxK2NRSk5KcU9MYVhMaXA5djJpYXd4SWJLeXhPdDJQYmtRK2tEMGtadS9FYWhSNzdBendPRC9lYmFRZ1kvUC9ZaTQvYkg0TnZHTG1hbjRGdXBoMFNGL2kvVEs4a0luNGVkT0NkdmlYV2NSUT09IiwibWFjIjoiNDc0MDlkNDUyMGJkYjBkOWE3YmI2MWU3NmE4ZGQ2OGM2NTg5YzI3ZTMxYTIxNDU2MDMzOTBmOWZiMDg0NGEyZCIsInRhZyI6IiJ9; ip_address=eyJpdiI6IllSZnB5R245MHlnejNNb0Z6MENMWEE9PSIsInZhbHVlIjoicmkwekJvY0F0MVVqT1VGV2VObkF1blFGdHZrbHhYOEFHTjVIOFp2UHJqUnQwdkFHVjhpQ3BBMGlZL0owK1liZ1huZzIwY3pQOXFvNXpoUko1M3RTb3c9PSIsIm1hYyI6Ijg4NjBmOTUxZjdjZThhY2VjYTU0Mzg3ZDc0N2YzNTAzYWIxNDRkZjkzNzJmMDNkMmQ3NzUwZTI4NmQwMDVlM2EiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjZkamlpRmFaQlhuVmQ3VjBjSDVWakE9PSIsInZhbHVlIjoidUV6YmZRQ1UwYUNDUDZJVndkY0p0djRRSzJVRVZOd2lNcTE4cFluZmVlWGplTXR5cWN0Y0JReHNsN3BFa1lZTSIsIm1hYyI6ImQ4OTI1ODQxMWM1ODliN2JmMWYzOGExNGZhOTkyYTc1ZmQxNDQyNzA0OTkwYjkzMGIwMmFjN2U3MWI1ZWIxOWIiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IkVCdkR1QjB5c2RDdnZJZHZUQStLYWc9PSIsInZhbHVlIjoiSlFlZ2FYaVJDUkxucU9ERjBJL3VUWjZ6QWJJNkFFWnRLNWxuQ0dkVEpJZllTVUZDc0VvY2VlL0JyQTFVNmNTa2JPWlpKa0NLbFFCbVZTN0FFUFNjZlE9PSIsIm1hYyI6IjE0ZjBhMTI5YTdlZjFiMGQxN2MwY2E5NjlkNjYyYjU2ZGZiYmJhZDI2ZDBlNWM1NGQ4MTVmM2ExODg0ZDM5ZGIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkI5c0NRUWdia2xwbWhxWGhBVy9lTUE9PSIsInZhbHVlIjoiemNSUGY0UXF5QnNZVk9LM25UOVg5QnYrbFpycmNIR3dWVjY0YkR5SnZJcjR3QlFRM21PbFZESWZndWR2TmtuWXhSK0NYZVJGejNPNzlmaHphRE5WWXdPRk4yeDVMaHJJeXpIYjR3SnBHOFJWdUJISVhKRkdkQUJseDJGUGJpK3EiLCJtYWMiOiJkY2NhZmMyZTZmMzNkMDQ0MDA0M2EyZjY5MzFiMzYzMzhmNjQzNzM1MjlmMWEyMjY5OWQ0MzJjYTc0NmMwOTAzIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IlZWc3hkMTZCWXlHV1Z2dk0wUjBYVGc9PSIsInZhbHVlIjoiNDZNbVhwUnlEbkRORmZOMHlzVGxvcDlpbGZSVER1cXkvWWZFMjlPUHliMVNPMy80Uk5rZ0RMZk45eS8xc2JLYlpUaGs3VXBoeWs5T2ZzSGRqNis4MmRIVk43OWVzVGZHclpxd1Mwb004UlN3MFlReGpnclNwOEE3N3FYdHZpaVIiLCJtYWMiOiIxYmM5ZjdiNzU4ODg1MTMyMGMxNWQ1N2M5MTdkMGE3YWJlYTZkM2NlMmE1YWEwYmFjNzUzMTE3ZmQ5ZjNmY2I3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071944681\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-103683558 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TckR1LWzJcECAEEV9U5D46L3vYjTeYlIxc6zCKj5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103683558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1175781323 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 23 Jun 2025 09:24:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImYxVUlGQ3BVY3d3OWU1WGdsMUZ2c3c9PSIsInZhbHVlIjoiMUt1K3lIUHNjZTNaVXZMV2lOMUxIeU92czBOSzRuSjhqYmx0WTQyWGFQcllkQ0FmNmR2blcveWx6NkdwcDhWZHk3TGNzSFZqZGFTek1zZ0tCeDlnYVpaSUErcU5tenRwWjF3cUFUYjRlbWtNemMxTjRZWkNBMUNZSVFwUkRPWEYiLCJtYWMiOiI4MzAyOWRkYzMxZTk3NDA1OTM3ZTM5OWNlNTliN2VjYjk3YWYzYTI2MmNjYWQ5YzY0NTNkMGFiNjMxOTRjMzdkIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:24:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6ImVJZ1huV0NRWndFTnQyUUVhZGF5NVE9PSIsInZhbHVlIjoiTEh5V2lMSG1tQWppTWJwV2Y5c2tsOTRBM3ZtbHVaanJwajVZU0VraDQzd09lMVlpQ0dxclhsaEVQcXQyWGZXWkszdk16bU56VC9MZ0tkWDB3eGVsaU9aREswUk9IbHNBZjVuak8wTk5JdWQ2MWE4R2FPeDdQd0F5UzAzSnFIOW0iLCJtYWMiOiJiNmNjNmNkYmVhYTk1YzQ1OTQ4NGFhZDk0YTUzZjI2ZDA1YjQxNWE0MDlmYjQyNzJlYjcxZjVmNTRhZjU4Yjc3IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:24:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImYxVUlGQ3BVY3d3OWU1WGdsMUZ2c3c9PSIsInZhbHVlIjoiMUt1K3lIUHNjZTNaVXZMV2lOMUxIeU92czBOSzRuSjhqYmx0WTQyWGFQcllkQ0FmNmR2blcveWx6NkdwcDhWZHk3TGNzSFZqZGFTek1zZ0tCeDlnYVpaSUErcU5tenRwWjF3cUFUYjRlbWtNemMxTjRZWkNBMUNZSVFwUkRPWEYiLCJtYWMiOiI4MzAyOWRkYzMxZTk3NDA1OTM3ZTM5OWNlNTliN2VjYjk3YWYzYTI2MmNjYWQ5YzY0NTNkMGFiNjMxOTRjMzdkIiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:24:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6ImVJZ1huV0NRWndFTnQyUUVhZGF5NVE9PSIsInZhbHVlIjoiTEh5V2lMSG1tQWppTWJwV2Y5c2tsOTRBM3ZtbHVaanJwajVZU0VraDQzd09lMVlpQ0dxclhsaEVQcXQyWGZXWkszdk16bU56VC9MZ0tkWDB3eGVsaU9aREswUk9IbHNBZjVuak8wTk5JdWQ2MWE4R2FPeDdQd0F5UzAzSnFIOW0iLCJtYWMiOiJiNmNjNmNkYmVhYTk1YzQ1OTQ4NGFhZDk0YTUzZjI2ZDA1YjQxNWE0MDlmYjQyNzJlYjcxZjVmNTRhZjU4Yjc3IiwidGFnIjoiIn0%3D; expires=Mon, 23-Jun-2025 11:24:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175781323\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-872680910 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I42VsBX0l5ewyFcnvCuflv6iNTRW70U72KiP2FRT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"65 characters\">http://127.0.0.1:8000/pusher/beams-auth?user_id=127-0-0-1-admin-1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750659774</span>\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872680910\", {\"maxDepth\":0})</script>\n"}}