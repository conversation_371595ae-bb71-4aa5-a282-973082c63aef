{"__meta": {"id": "X36818170a098d707075673e201f77149", "datetime": "2025-06-22 15:24:19", "utime": **********.486487, "method": "GET", "uri": "/admin/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.214177, "end": **********.486506, "duration": 0.27232909202575684, "duration_str": "272ms", "measures": [{"label": "Booting", "start": **********.214177, "relative_start": 0, "end": **********.444456, "relative_end": **********.444456, "duration": 0.23027920722961426, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.444472, "relative_start": 0.*****************, "end": **********.486508, "relative_end": 1.9073486328125e-06, "duration": 0.042035818099975586, "duration_str": "42.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET admin", "middleware": "web, guest, admin.login.guard", "uses": "Closure() {#613\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#602 …}\n  file: \"D:\\projects\\CardApp\\routes\\auth.php\"\n  line: \"14 to 16\"\n}", "as": "admin.", "namespace": null, "prefix": "/admin", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Froutes%2Fauth.php&line=14\" onclick=\"\">routes/auth.php:14-16</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00129, "accumulated_duration_str": "1.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.467844, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 41.085}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "guest", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.476868, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 41.085, "width_percent": 58.915}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750583843\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/", "status_code": "<pre class=sf-dump id=sf-dump-509732808 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-509732808\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1779056518 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1779056518\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1779891471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1779891471\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-926288602 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlVMZytZQS9HWnBHbTAzK2FvTzZVOEE9PSIsInZhbHVlIjoiN1J5S0o4NldYWkVEd3I1S2pCTUlwZ3JaamRxV05XU1JjWHM1M3N6TGE2SExzbTMzTnBpLzVUUHZqZXB2Y2piWGJla0FTZnEwbFRZTS9mVjVlZ3VCQ0N4MjVTcEtmL2gvNjAwQ0hWeHhIczA4a1BHMlpwakg2NjNlcURqMGJkWFIiLCJtYWMiOiI1Y2MzNTY1M2QxZDIwY2VlZmQxNWE5NjEzYmUzZmE4ZDhlMTczNzllNjVhODhkMmJkNDVmMDNlYzc4ZDFlYmVmIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6Ikl1WkZKb0crTmdaRnp3TUd1MFdHTEE9PSIsInZhbHVlIjoiTFV4WWk3RlRINHhHajcvQjV5S3NtMVJlSGljMVhGWDgvYUV3RHhlQnA1Z0ltMFVLSmFPMUkwdkZxcDBJSVBsMVFvalRPanRCeGdMbG03OFk3VjZGZnJBNWZ4cU85S3cyMUJoMDY3bTMzemJsRWZBc3QxSS82UGxKejNkc0Ntd0kiLCJtYWMiOiI5ZWU2NTE5Mzk5YjE1MzA0NTc4YjkzM2M3ZjM4ZTU2MWYxYTM0M2YzODBlZjk5NTczMGUwMDY0ZDY2ZjRkNDYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926288602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SJcBaZ7iTTWzVst4B6KdlZjJwU9k5yXwCvFs50gr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1249586745 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:24:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhuQjBTZk9kT2NBdGVCU2pCaUU4R0E9PSIsInZhbHVlIjoiT2NlRzVWNlQ5MkloVWlnbzN5MHdpT0FYcGE1ckFOd1k2b1UwR29kTjhnUVUxN1JXRXhjNk50TG9Vdk1kYm5SSVBDYW9NaTBzR21ENHYrQnoxckRUMUhXRVE4NFJSOUZtZk5rMS9ENmVoQUhkZjhyekUyMkJOOFJlL2lCN3BCN1QiLCJtYWMiOiIxNWFiMjFlOGM3ZDNiODNlNzRmZjQzMmJmMjY3MzEwNGM0ZWM2MjU3NjAwYjYxM2EzY2M0YzEzNzNhY2M2ZTYzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:24:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IjAyWEVQeVBuQ2IwdDlOMmgyUjYzOWc9PSIsInZhbHVlIjoidnprMWpLN2ZKL2ZMejNEUUNWKzI3dnI4cVdQOU5kM29aeFArYzNyc0lXOHZySkY0VDd2dGlVR2llUjJCclhZa2thNVpUUG56b2ZWNUN2MDlqK3dwM1dkQXo4dG5ZUThXRmFidHlPcWhoQ3llMjN6SGgyNUQ2U0lRcGwrRUo4NE0iLCJtYWMiOiJjNzg4NzMzMjdmOTQzOGRkOWI2ZjZmN2YwZjQ1MWExYTRkZjlmNjYwMTZhYjQ0MWQ4YzQzMjE2NzE0MTZhNzAxIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:24:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhuQjBTZk9kT2NBdGVCU2pCaUU4R0E9PSIsInZhbHVlIjoiT2NlRzVWNlQ5MkloVWlnbzN5MHdpT0FYcGE1ckFOd1k2b1UwR29kTjhnUVUxN1JXRXhjNk50TG9Vdk1kYm5SSVBDYW9NaTBzR21ENHYrQnoxckRUMUhXRVE4NFJSOUZtZk5rMS9ENmVoQUhkZjhyekUyMkJOOFJlL2lCN3BCN1QiLCJtYWMiOiIxNWFiMjFlOGM3ZDNiODNlNzRmZjQzMmJmMjY3MzEwNGM0ZWM2MjU3NjAwYjYxM2EzY2M0YzEzNzNhY2M2ZTYzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:24:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IjAyWEVQeVBuQ2IwdDlOMmgyUjYzOWc9PSIsInZhbHVlIjoidnprMWpLN2ZKL2ZMejNEUUNWKzI3dnI4cVdQOU5kM29aeFArYzNyc0lXOHZySkY0VDd2dGlVR2llUjJCclhZa2thNVpUUG56b2ZWNUN2MDlqK3dwM1dkQXo4dG5ZUThXRmFidHlPcWhoQ3llMjN6SGgyNUQ2U0lRcGwrRUo4NE0iLCJtYWMiOiJjNzg4NzMzMjdmOTQzOGRkOWI2ZjZmN2YwZjQ1MWExYTRkZjlmNjYwMTZhYjQ0MWQ4YzQzMjE2NzE0MTZhNzAxIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:24:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249586745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1293585171 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750583843</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293585171\", {\"maxDepth\":0})</script>\n"}}