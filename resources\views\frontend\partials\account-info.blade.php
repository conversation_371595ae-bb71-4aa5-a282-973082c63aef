@php
    $app_mode = strtolower(env('APP_MODE'));
    $current_route = request()->path();
    $modal_class_routes = ['/', 'privacy-policy'];
    $is_modal_home = in_array($current_route, $modal_class_routes);
@endphp
<section class="account-section">
    <div class="account-bg"></div>
    <div class="account-area change-form {{ $is_modal_home ? 'modal__home' : '' }}">

        <!-- <div class="account-close"></div> -->

        <div class="account-form-area">
            <div class="modal-header">
                <h5 class="modal-title" id="send_confirmationLabel">
                {{ __("Login Information") }}
                </h5>
                <button
                type="button"
                class="btn-close bg-transparent account-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                >
                <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
                </button>
          </div>
            <form action="{{ setRoute('user.login.submit') }}" method="POST" class="account-form">
                @csrf
                <div class="row ml-b-20">
                    <div class="col-lg-12 form-group">
                        <label for="credentials">{{__("Email")}}</label>
                        <input type="email" required class="form-control form--control" name="credentials" placeholder="{{__("Email")}}" spellcheck="false" data-ms-editor="true" value="{{@$app_mode == 'demo' ? '<EMAIL>': old('credentials') }}">
                    </div>
                    <div class="col-lg-12 form-group show_hide_password">
                    <label for="password">{{ __("password") }}</label>
                        <input type="password" name="password" class="form-control form--control  "  placeholder="{{ __("password") }}" required value="{{ @$app_mode == 'demo' ? 'appdevs':'' }}">
                        <a href="javascript:void(0)" class="show-pass"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>

                    </div>
                    <div class="col-lg-12 form-group">
                        <div class="forgot-item">
                            <label><a href="{{ setRoute('user.password.forgot') }}">{{ __("forget Password") }}</a></label>
                        </div>
                    </div>
                    <div class="col-lg-12 form-group text-center">
                        <button type="submit" class="btn--base w-100 btn-loading">{{ __("Login Now") }}</button>
                    </div>

                    <!-- social login  start-->
                    <div class='d-flex flex-column align-items-center justify-content-center gap-2 w-100 mt-4'>
                        <button  type="button"
                            class='d-flex align-items-center justify-content-center gap-1 w-100 bg-transparent border p-2 rounded-3 text--primary fw-bold' 
                            style="font-size:14px" 
                            onclick="location.href='{{ url('auth/google') }}'">
                            <img src="{{ asset('frontend/assets/icons/google.svg') }}" height="24" width="24" />
                            <span>{{ __("login with google") }}</span>
                        </button>
                        
                        <button type="button" 
                            class='d-flex align-items-center justify-content-center gap-1 w-100 bg-transparent border p-2 rounded-3 text--primary fw-bold' 
                            style="font-size:14px" 
                            onclick="location.href='{{ url('auth/facebook') }}'">
                            <img src="{{ asset('frontend/assets/icons/facebook.svg') }}" height="24" width="24" />
                            <span>{{ __("login with facebook") }}</span>
                        </button>
                    </div>
                    
                    <!-- social login  end-->

                    <div class="or-area">
                        <span class="or-line"></span>
                        <span class="or-title">Or</span>
                        <span class="or-line"></span>
                    </div>
                    @if($basic_settings->user_registration)
                    <div class="col-lg-12 text-center">
                        <div class="account-item">
                            <label>{{ __("Don't Have An Account?") }} <a href="javascript:void(0)" class="account-control-btn">{{ __("Register Now") }}</a></label>
                        </div>
                    </div>
                    @endif
                </div>
            </form>
        </div>
    </div>
    <div class="account-area {{ $is_modal_home ? 'modal__home' : '' }}">
        <div class="account-close"></div>
        <div class="account-form-area">
            <div class="modal-header">
            <h5 class="modal-title" id="send_confirmationLabel">
            {{__("Register Information")}}
            </h5>
            <button
              type="button"
              class="btn-close bg-transparent account-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            >
              <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
            </button>
          </div>
            <!-- <h5 class="title">{{__("Register Information")}}</h5>
            <p>{{ __("Please input your details and register to your account to get access to your dashboard.") }}</p> -->
            <form class="account-form" action="{{ setRoute('user.register.submit') }}" method="POST">
                @csrf
                <div class="row ml-b-20">
                    <div class="col-lg-6 form-group">
                    <label for="firstname">{{ __("first Name") }}</label>
                            <input type="text" class="form-control form--control" name="firstname" placeholder="{{ __("first Name") }}" required value="{{ old('firstname') }}">
                    </div>
                    <div class="col-lg-6 form-group">
                    <label for="lastname">{{ __("last Name") }}</label>
                    <input type="text" class="form-control form--control" name="lastname" placeholder="{{ __("last Name") }}" required value="{{ old('lastname') }}">
                    </div>
                    <div class="col-lg-12 form-group">
                    <label for="register_email">{{ __("Email") }}</label>
                            <input type="email" class="form-control form--control" name="register_email" placeholder="{{ __("Email") }}" required value="{{ old('register_email') }}">
                    </div>
                    <div class="col-lg-12 form-group show_hide_password">
                    <label for="register_password">{{ __("password") }}</label>
                            <input type="password" name="register_password" class="form-control form--control" required placeholder="{{ __("password") }}">
                            <a href="javascript:void(0)" class="show-pass"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
                    </div>
                        @if($basic_settings->agree_policy)
                    <div class="col-lg-12 form-group">
                        <div class="custom-check-group">
                            <input type="checkbox" id="level-1" name="agree" required>
                            <label for="level-1">{{ __("I have agreed with") }} <a href="{{ setRoute('privacy.policy') }}" target="_blank">{{ __("Terms Of Use & Privacy Policy") }}</a></label>
                        </div>

                    </div>
                    @endif
                    <div class="col-lg-12 form-group text-center mt-4">
                        <button type="submit" class="btn--base w-100 btn-loading">{{__("Register Now")}}</button>
                    </div>
                    <div class="col-lg-12 text-center">
                        <div class="account-item">
                            <label>{{ __("Already Have An Account?") }} <a href="javascript:void(0)" class="account-control-btn">{{ __("Login Now") }}</a></label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

@push('script')
@php
     $errorName ='';
@endphp
@if($errors->any())
@php
    $error = (object)$errors;
    $msg = $error->default;
    $messageNames  = $msg->keys();
    $errorName = $messageNames[0];
@endphp
@endif
<script>
    var error = "{{  $errorName }}";
  if(error == 'credentials' ){
    $('.account-section').addClass('active');
  }
  if(
    error == 'firstname' ||
    error == 'agree' ||
    error == 'register_password' ||
    error == 'register_email' ||
    error == 'lastname'
  ){
    $('.account-section').addClass('active');
    $('.account-area').toggleClass('change-form');
  }
</script>
@endpush
