@extends('frontend.layouts.master')
@php
    $lang = selectedLang();
    $contact_slug = Illuminate\Support\Str::slug(App\Constants\SiteSectionConst::CONTACT_SECTION);
    $contact_sections = App\Models\Admin\SiteSections::getData($contact_slug)->first();
@endphp

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/contact.css') }}">
@endpush

@section('content')

<main id="main" class="container">
    <div class="row">
        <!-- right section start -->
        <div id="rightSection" class="col-12 col-md-6 order-2 order-md-1 mb-4 mb-md-0">
            <div class="rightSection__img__wrapper position-relative">
                <img src="{{ asset('frontend/assets/contact_img.svg') }}" alt="{{ __('contact_image_alt') }}" class="w-100 object-fit-cover" />
                <div id="border" class="position-absolute"></div>
            </div>
        </div>
        <!-- right section end -->

        <!-- left section start -->
        <div id="leftSection" class="col-12 col-md-6 order-1 order-md-2 mb-4 mb-md-0 text-center text-md-start">
            <div class="d-flex flex-column justify-content-start align-items-start gap-3">
                <span id="badge" class="align-self-center align-self-md-start">{{ __('contact_us_badge') }}</span>
                <h1>
                    {{ __('card_app') }}
                    <br />
                    <span>{{ __('contact_now') }}</span>
                </h1>
                <p>{{ __('contact_message') }}</p>
            </div>

            <form class="d-flex flex-column gap-3" action="{{ route('contact.store') }}" method="POST">
                @csrf
                <div class="row">
                    <div class="col-12 col-md-6">
                        <div class="input__wrapper">
                            <input type="text" name="name" placeholder="{{ __('name_placeholder') }}" class="w-100" />
                            <img src="{{ asset('frontend/assets/user.svg') }}" alt="{{ __('name_icon_alt') }}" />
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="input__wrapper">
                            <input type="email" name="email" placeholder="{{ __('email_placeholder') }}" class="w-100" />
                            <img src="{{ asset('frontend/assets/mail.svg') }}" alt="{{ __('email_icon_alt') }}" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-6">
                        <div class="input__wrapper">
                            <input type="number" name="mobile" placeholder="{{ __('phone_placeholder') }}" class="w-100" />
                            <img src="{{ asset('frontend/assets/phone.svg') }}" alt="{{ __('phone_icon_alt') }}" />
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="input__wrapper">
                            <input type="text" name="subject" placeholder="{{ __('subject_placeholder') }}" class="w-100" />
                            <img src="{{ asset('frontend/assets/subject.svg') }}" alt="{{ __('subject_icon_alt') }}" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <textarea name="message" id="message" cols="50" rows="5" placeholder="{{ __('message_placeholder') }}" class="w-100 text--dark"></textarea>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary w-100 rounded-3">{{ __('submit_button') }}</button>
            </form>
        </div>
        <!-- left section end -->
    </div>
</main>

@endsection

@push('script')
@endpush
