@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
<link
rel="stylesheet"
href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css"
integrity="sha384-gXt9imSW0VcJVHezoNQsP+TNrjYXoGcrqBZJpry9zJt8PCQjobwmhMGaDHTASo9N"
crossorigin="anonymous"
/>
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')

<div class="px-4">
<section class="transactions d-flex flex-column">
  <div class="transactions__header">
    <p>سجل معاملات البطاقة</p>
    <input
      type="text"
      class="form-control"
      id="inlineFormInputGroupUsername"
      placeholder="البحث"
    />
  </div>
  <ul class="timeline w-100">
    @include('user.components.card-log',compact("transactions"))

   
  </ul>
  <!-- End -->
</section>
</div>

@endsection

@push('script')

<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>

@endpush
