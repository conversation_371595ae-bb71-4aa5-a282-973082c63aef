<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Terms and Conditions') }}</title>
    <link rel="stylesheet" href="{{ asset('frontend/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('frontend/css/index.css') }}">
    <link rel="shortcut icon" href="{{ get_fav($basic_settings) }}" type="image/x-icon">
    <link rel="stylesheet" href="{{ asset('frontend/css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ asset('frontend/css/hero.css') }}">
    <link
        href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
        rel="stylesheet"
        />

    
        <style>
    body {
        font-family: "Tajawal", sans-serif;
        font-weight: 400;
        font-style: normal;
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .priv_header {
        display: flex;
        justify-content: space-between;
        padding: 24px 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .priv_main {
        text-align: start;
        display: flex;
        flex-direction: column;
        justify-content: start;
    }

    .chip {
        background-color: rgba(99, 91, 254, 0.05);
        border-radius: 36px;
        color: #635BFE;
        width: fit-content;
        margin: 0;
        padding: 8px 16px;
    }

    .container {
        padding: 20px;
        text-align: justify;
        max-width: 1200px;
        margin: 0 auto;
    }

    h1 {
        font-size: 24px;
        color: black !important;
        margin: 0;
        margin-bottom: 20px;
    }

    .terms-list {
        list-style-type: decimal;
        padding: 0 20px;
        margin: 0;
    }

    .container p, .terms-list {
        color: #556987;
    }

    .terms-list li {
        margin-bottom: 10px;
        font-size: 18px;
        line-height: 1.6;
    }

    @media (min-width: 768px) {
        .priv_header {
            padding: 24px 50px;
        }

        .container {
            padding: 20px 50px;
        }
    }

    @media (min-width: 1024px) {
        .priv_header {
            padding: 24px 100px;
        }

        .container {
            padding: 20px 100px;
        }
    }

    /* Adjustments for small screens */
    @media (max-width: 767px) {
        .priv_header {
            flex-direction: column;
            align-items: center;
        }

        .priv_header img {
            order: -1; /* Move the logo to the top */
            margin-bottom: 20px;
        }

        .header-account-btn {
            margin: 10px;
            order:2;
        }

        h1 {
            font-size: 20px;
        }

        .terms-list li {
            font-size: 16px;
        }
    }
</style>



      
</head>
<body>
    @php
    $session_lan = session('local')??get_default_language_code();
    @endphp
    <header class="priv_header">
        @auth
            @if(auth()->user()->email_verified == 0)
            <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
            @else
             <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{ __('Dashboard') }}</a>
            @endif

        @else
        <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
        @endauth
        {{-- <div class="language-dropdown">
            <button class="dropdown-btn">
                <img width='20' src="{{ asset('frontend/assets/icons/language-switcher.svg') }}" alt="language switcher">
            </button>
            <ul class="dropdown-menu p-2">
                @foreach($__languages as $item)
                    <li class='nav-item'>
                        <a href="{{ route('lang', ['lang' => $item->code ]) }}" data-value="{{$item->code}}" style="width:100%;margin:5px" class="@if( $session_lan == $item->code) selected @endif">{{ $item->name }}</a>
                    </li>
                @endforeach
            </ul>
        </div> --}}
        <a href="{{ url('/') }}"> 
            <img src="{{ asset('frontend/assets/logo.svg') }}" alt="logo">
        </a>
    </header>
    @include('frontend.partials.account-info')
    <main dir="{{ __('dir') }}" class="priv_main">
        <div class="container">
            <span class="chip">{{ __('Terms and Conditions') }}</span>
            <h1>{{ __('Important Terms and Conditions for Using CARD APP') }}</h1>
            <p>
                {{ __('Please read the following terms and conditions carefully before using our services. By accessing our website and using any of our financial services, including withdrawal, deposit, sending and receiving money, and issuing electronic cards, you agree to comply with these terms.') }}
            </p>
            <ul class="terms-list">
                <li>{{ __('Use of Services: You commit to using our financial services only for legal and legitimate purposes. We reserve the right to suspend or terminate your account if any illegal or unauthorized activity is detected.') }}</li>
                <li>{{ __('Account Management: Users must provide accurate and complete information when creating their accounts. You are responsible for maintaining the confidentiality of your login credentials.') }}</li>
                <li>{{ __('Fees and Commissions: Fees may be charged for some of the services we offer. These fees will be clarified during transactions, and users must agree to them before proceeding.') }}</li>
                <li>{{ __('Payments and Transfers: All withdrawal, deposit, and money transfer operations are conducted using secure systems that comply with international standards. However, some transactions may be subject to additional verification before completion.') }}</li>
                <li>{{ __('Electronic Cards: Electronic cards are issued upon request and are subject to the specific terms for each type of card. Users are responsible for using the card correctly and within the agreed terms.') }}</li>
                <li>{{ __('Security Policies: We follow advanced security procedures to protect your personal and financial information. However, you agree to take responsibility in case of any breach resulting from user negligence in protecting their data.') }}</li>
                <li>{{ __('Changes to Terms: We reserve the right to modify these terms and conditions at any time. Users will be informed of any significant changes via email or through the website.') }}</li>
                <li>{{ __('By using our services, you agree to comply with all the above-mentioned terms and conditions. If you have any inquiries, please contact our support team for further information.') }}</li>
            </ul>
        </div>
    </main>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(".header-account-btn").on("click", function () {
        $(".account-section").addClass("active");
    });

    $(".account-control-btn").on("click", function () {
        $(".account-area").toggleClass("change-form");
    });

    $(document).ready(function() {
                var $dropdownBtn = $(".dropdown-btn");
                var $dropdownMenu = $(".dropdown-menu");
                var $dropdownItems = $(".dropdown-menu li");

                // Toggle the dropdown menu visibility
                $dropdownBtn.on("click", function() {
                    $dropdownMenu.toggleClass("show");
                });

                //  ! language switcher end

                //  ! navbar active page start

                var currentLocation = location.href.split('/').pop();
                var $navLinks = $(".nav-link");

                $navLinks.each(function() {
                    var $link = $(this);
                    if ($link.attr('id') === currentLocation) {
                        $link.addClass('active');
                    } else {
                        $link.removeClass('active');
                    }
                });
                //  ! navbar active page end
            });

            $(".account-close, .account-bg").on("click", function () {
        $(".account-section").addClass("duration");
        setTimeout(signupRemoveClass, 200);
        setTimeout(signupRemoveClass2, 200);
    });
    function signupRemoveClass() {
        $(".account-section").removeClass("active");
    }
    function signupRemoveClass2() {
        $(".account-section").removeClass("duration");
    }
</script>
<script src="{{ asset('frontend/js/bootstrap.bundle.js') }}"></script>
<script src="{{ asset('frontend/js/main.js') }}"></script>


</body>
</html>



