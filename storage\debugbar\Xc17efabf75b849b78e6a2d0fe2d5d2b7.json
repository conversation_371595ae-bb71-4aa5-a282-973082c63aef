{"__meta": {"id": "Xc17efabf75b849b78e6a2d0fe2d5d2b7", "datetime": "2025-06-22 15:16:39", "utime": **********.040736, "method": "GET", "uri": "/user/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.26687, "end": **********.040754, "duration": 0.7738840579986572, "duration_str": "774ms", "measures": [{"label": "Booting", "start": **********.26687, "relative_start": 0, "end": **********.560211, "relative_end": **********.560211, "duration": 0.29334092140197754, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.560226, "relative_start": 0.29335594177246094, "end": **********.040756, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29453848, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 11, "templates": [{"name": "notifications::email", "param_count": null, "params": [], "start": **********.645553, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications/resources/views/email.blade.phpnotifications::email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2Fresources%2Fviews%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}}, {"name": "mail::message", "param_count": null, "params": [], "start": **********.884975, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/html/message.blade.phpmail::message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}}, {"name": "mail::header", "param_count": null, "params": [], "start": **********.893217, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/html/header.blade.phpmail::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "mail::footer", "param_count": null, "params": [], "start": **********.916784, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/html/footer.blade.phpmail::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "mail::layout", "param_count": null, "params": [], "start": **********.962215, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/html/layout.blade.phpmail::layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}, {"name": "mail::themes.default", "param_count": null, "params": [], "start": **********.98393, "type": "css", "hash": "cssD:\\projects\\CardApp\\resources\\views/vendor/mail/html/themes/default.cssmail::themes.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Fhtml%2Fthemes%2Fdefault.css&line=1", "ajax": false, "filename": "default.css", "line": "?"}}, {"name": "notifications::email", "param_count": null, "params": [], "start": **********.998907, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications/resources/views/email.blade.phpnotifications::email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FNotifications%2Fresources%2Fviews%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}}, {"name": "mail::message", "param_count": null, "params": [], "start": **********.999705, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/text/message.blade.phpmail::message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}}, {"name": "mail::header", "param_count": null, "params": [], "start": **********.013519, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/text/header.blade.phpmail::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "mail::footer", "param_count": null, "params": [], "start": **********.015408, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/text/footer.blade.phpmail::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "mail::layout", "param_count": null, "params": [], "start": **********.016941, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/vendor/mail/text/layout.blade.phpmail::layout", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fvendor%2Fmail%2Ftext%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "route": {"uri": "GET user/dashboard", "middleware": "web, auth, verification.guard", "controller": "App\\Http\\Controllers\\User\\DashboardController@index", "as": "user.dashboard", "namespace": null, "prefix": "/user", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=23\" onclick=\"\">app/Http/Controllers/User/DashboardController.php:23-43</a>"}, "queries": {"nb_statements": 6, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007949999999999999, "accumulated_duration_str": "7.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `virtual_card_apis` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 881}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 706}], "start": **********.5784721, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:20", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/User/DashboardController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\DashboardController.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FDashboardController.php&line=20", "ajax": false, "filename": "DashboardController.php", "line": "20"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 7.799}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.593347, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 7.799, "width_percent": 8.176}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.5966501, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 15.975, "width_percent": 6.164}, {"sql": "select exists(select * from `user_authorizations` where `token` = 'EiCx7UNaiVBXjvTZDX4OA7ACnjVzKF8hP6xmX56PQB2tp7jvFUtXiXVusgeGUCFSzms8BGb5GrxJmeUVXHWXifWg3i1zjogJBcFUMddOkZYESCnsxpfBBbhaMSRvmTSgvJvaWZBTDTCyUM5lmN3UMERRlVH4RWxNYlBTz2U4e7AxxtzkYrFqsuE99ROZqNnsJPdiQ5DT') as `exists`", "type": "query", "params": [], "bindings": ["EiCx7UNaiVBXjvTZDX4OA7ACnjVzKF8hP6xmX56PQB2tp7jvFUtXiXVusgeGUCFSzms8BGb5GrxJmeUVXHWXifWg3i1zjogJBcFUMddOkZYESCnsxpfBBbhaMSRvmTSgvJvaWZBTDTCyUM5lmN3UMERRlVH4RWxNYlBTz2U4e7AxxtzkYrFqsuE99ROZqNnsJPdiQ5DT"], "hints": null, "show_copy": true, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1301}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 33}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.6014059, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1301", "source": {"index": 9, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1301}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1301", "ajax": false, "filename": "helpers.php", "line": "1301"}, "connection": "cardapp", "explain": null, "start_percent": 22.138, "width_percent": 9.937}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1429}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 11, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 33}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.605077, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "helpers.php:1429", "source": {"index": 8, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1429}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1429", "ajax": false, "filename": "helpers.php", "line": "1429"}, "connection": "cardapp", "explain": null, "start_percent": 32.075, "width_percent": 0}, {"sql": "delete from `user_authorizations` where `user_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1431}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 14, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.605809, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1431", "source": {"index": 11, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1431}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1431", "ajax": false, "filename": "helpers.php", "line": "1431"}, "connection": "cardapp", "explain": null, "start_percent": 32.075, "width_percent": 7.547}, {"sql": "insert into `user_authorizations` (`user_id`, `code`, `token`, `created_at`) values (3, '9297', 'EiCx7UNaiVBXjvTZDX4OA7ACnjVzKF8hP6xmX56PQB2tp7jvFUtXiXVusgeGUCFSzms8BGb5GrxJmeUVXHWXifWg3i1zjogJBcFUMddOkZYESCnsxpfBBbhaMSRvmTSgvJvaWZBTDTCyUM5lmN3UMERRlVH4RWxNYlBTz2U4e7AxxtzkYrFqsuE99ROZqNnsJPdiQ5DT', '2025-06-22 15:16:38')", "type": "query", "params": [], "bindings": [3, "9297", "EiCx7UNaiVBXjvTZDX4OA7ACnjVzKF8hP6xmX56PQB2tp7jvFUtXiXVusgeGUCFSzms8BGb5GrxJmeUVXHWXifWg3i1zjogJBcFUMddOkZYESCnsxpfBBbhaMSRvmTSgvJvaWZBTDTCyUM5lmN3UMERRlVH4RWxNYlBTz2U4e7AxxtzkYrFqsuE99ROZqNnsJPdiQ5DT", "2025-06-22 15:16:38"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1432}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 33}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.6074, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "helpers.php:1432", "source": {"index": 10, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1432}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1432", "ajax": false, "filename": "helpers.php", "line": "1432"}, "connection": "cardapp", "explain": null, "start_percent": 39.623, "width_percent": 60.377}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1436}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 11, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 33}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.031943, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "helpers.php:1436", "source": {"index": 8, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1436}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1436", "ajax": false, "filename": "helpers.php", "line": "1436"}, "connection": "cardapp", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\VirtualCardApi": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FVirtualCardApi.php&line=1", "ajax": false, "filename": "VirtualCardApi.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/user/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "error": "array:1 [\n  0 => \"حدث خطأ ما! يرجى المحاولة مرة أخرى\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/user/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-800011058 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-800011058\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1416148170 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1416148170\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1041854478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1041854478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618620552 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlRRNWY0cjNDK1VqRW8zVGtwemFOTHc9PSIsInZhbHVlIjoiVGpDc1AxR2FVNUJqRldFQVJYVHNIZG1VSjhCQlZXV1R1VjFBaDNMZDJGYlhENWp6dHJSRGQvRFZvNUM1QUhPbmNoci9ZN0dTT1UzWTR4cXhCQVNqdklqRE9reWxnVTJTS2N6eWRIbURHNHdFSmp5ZHRaNFZkc3VGUFJQRjJEVnoiLCJtYWMiOiIzMWMwNDYwZDU3ZGYyYzZmYTM1YzBlY2VhZGNhNjEyNjY4ZWU0MDI3M2Q2MGQwZmU5OGIwNjUwYWY2YzEwYjBjIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IjJlbDJiYlJUakRNWjV5VWZvTjBPREE9PSIsInZhbHVlIjoibGhYYWpBZ2RZdm1ObGZNd0NlaENpc0FnZHNUaEgzTWk5N2o1RTFScWgwbnU3Y2xSbDN6TWlIREtrYXBIME5jZEVXemU0VjRsdkRlakgxbFJTOERDTjQ4NkdUMHF4Sko2YXlHNlh4cXh4b3FESmI3dS9QQlh3bTRmVnlmZTlIMzYiLCJtYWMiOiI4NjQ3ZTQ2YjQxYmZlNjRiOGVlYWZhYzQ5YTRjMDdhMDA2ZjhhNjg3OGJkMWJjODkyNWE4MGIyZDU1NWU1NzYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618620552\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-614720494 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6hJB8uO6hRqblWYRUHX84avzRVBLyhWhjPxEukEh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614720494\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-235806321 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:16:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJ3aTFOSGtQdHZ6VERyQys2TmpZRnc9PSIsInZhbHVlIjoiMndla3V4TUJyTXN2RVVZamVBMmkrckRYcmUrVE93SVBhdWt1ODV2TUgzWWVjSE8xM0ttU0FWdkdiZDFIR1h4c1FTN3pGd1pmM3g2QlZsaWp4ajI4TnVqMXg0V1V1TmdGNUtQRzVtVXdJN2x6bVBMZi94dEhaR3h1THdSS2ZKT1IiLCJtYWMiOiI0ODE5YWYxNjMyYjg5NWIzYThlOWVlYzEyNjBkNWZjOWNmYjAwM2RhYjg2M2RlY2VmMzNlMzc5MmZhOWYyMTkzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IjZyVWZtRzExbHZpMmYvUlJ1SmRwalE9PSIsInZhbHVlIjoiMjEyNTlvRnZ3cXRVNWhZbWFqaVY4NFNDNWR2aFFqTHhCRXg5RGNiNUdQNEtzQ1d4UWdQZWJRTVpRc3F6eDRLd0VaUE5XYzF1WXFlZUxRK0wrYlBmV1l6N2lveS8zT2R3QjBDZm84TzZxdjVVcXYvMlA0cXpBRDNNdVh0dllRQzkiLCJtYWMiOiIzM2ZlYzMxMWViOTE0MDViMzU4ZTE3ZTFhYzA5ZGZkMWEzOTljZDFmZWI3MGM1ZTUwMzU1MzU4NjhkYzg5ZTZhIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJ3aTFOSGtQdHZ6VERyQys2TmpZRnc9PSIsInZhbHVlIjoiMndla3V4TUJyTXN2RVVZamVBMmkrckRYcmUrVE93SVBhdWt1ODV2TUgzWWVjSE8xM0ttU0FWdkdiZDFIR1h4c1FTN3pGd1pmM3g2QlZsaWp4ajI4TnVqMXg0V1V1TmdGNUtQRzVtVXdJN2x6bVBMZi94dEhaR3h1THdSS2ZKT1IiLCJtYWMiOiI0ODE5YWYxNjMyYjg5NWIzYThlOWVlYzEyNjBkNWZjOWNmYjAwM2RhYjg2M2RlY2VmMzNlMzc5MmZhOWYyMTkzIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IjZyVWZtRzExbHZpMmYvUlJ1SmRwalE9PSIsInZhbHVlIjoiMjEyNTlvRnZ3cXRVNWhZbWFqaVY4NFNDNWR2aFFqTHhCRXg5RGNiNUdQNEtzQ1d4UWdQZWJRTVpRc3F6eDRLd0VaUE5XYzF1WXFlZUxRK0wrYlBmV1l6N2lveS8zT2R3QjBDZm84TzZxdjVVcXYvMlA0cXpBRDNNdVh0dllRQzkiLCJtYWMiOiIzM2ZlYzMxMWViOTE0MDViMzU4ZTE3ZTFhYzA5ZGZkMWEzOTljZDFmZWI3MGM1ZTUwMzU1MzU4NjhkYzg5ZTZhIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235806321\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1823067170 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">&#1581;&#1583;&#1579; &#1582;&#1591;&#1571; &#1605;&#1575;! &#1610;&#1585;&#1580;&#1609; &#1575;&#1604;&#1605;&#1581;&#1575;&#1608;&#1604;&#1577; &#1605;&#1585;&#1577; &#1571;&#1582;&#1585;&#1609;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1823067170\", {\"maxDepth\":0})</script>\n"}}