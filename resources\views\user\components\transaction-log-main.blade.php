{{-- @extends('user.layouts.master') --}}

{{-- @section('content') --}}

<!-- transaction history section start -->
<section  class="transactions d-flex flex-column mt-4">
        <ul id="transactions-container" class="timeline w-100 dashboard-list-wrapper">
            @include('user.components.transaction-log',compact("transactions"))
        </ul>
        {{-- <div id="transactions-container"> --}}
    {{-- @include('user.components.load-more') --}}
{{-- </div> --}}
</section>

@if($transactions->hasMorePages())
    <div class="load-more-container text-center my-4">
        <button id="load-more-button" class="btn">
            <span>
                {{ __('Load More') }}
            </span>
            <img src="frontend/assets/down-gray-arrow.svg" alt="down arrow">
        </button>
    </div>
@endif

{{-- @endsection --}}


@push("script")
        <script>
        $(document).ready(function () {
    $('#transactions-container').on('click', '.transaction', function () {
        console.log(56456);

        $(this)
            .find('.transaction__card > .transaction__card_details')
            .toggleClass('--show');
    });
});

        </script>
    @endpush


