<!-- card details -->
@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="/frontend/css/custom/virtual-card.css" />
<link rel="stylesheet" href="/frontend/css/custom/transaction-history.css" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
      [
        'name'  =>__("Dashboard"),
        'url'   => setRoute("user.dashboard"),
        ],
      [
        'name'  =>__("Cards"),
        'url'   => setRoute("user.dashboard"),
        ],
    ], 'active' => __($page_title)])
@endsection

@section('content')

<section class="container-fluid justify-content-between border--primary p-4 rounded-3">
  <div class="row justify-content-between"> 
    <!-- details section -->
    <div class="card__details_des p-3 border col-xl-6" style="background: #CDCDCD1A;color: #000; font-size: 14px; display: flex; flex-direction: column; gap: 5px;height:fit-content;margin-top:10px">
      <h4 class='text-dark'>{{ __('Card Details') }}</h4>
        
      <!-- one detail start -->
      <div class="one_detail rounded-3 p-2 border d-flex align-items-center justify-content-between m-1">
          <p class='mb-0 copy-text'>{{ $result["cardNumber"] }}</p>
          <button class='copy-btn bg-transparent'>
            <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>
          </button>
       </div>

      <div class="d-flex align-items-center gap-2 w-100">
         <!-- one detail start -->
         <div class="one_detail rounded-3 p-2 border d-flex align-items-center justify-content-between w-100 m-1">
            <p class='mb-0 copy-text'>{{ $result["expiration"] }}</p>
            <button class='copy-btn bg-transparent'>
              <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>
            </button>
         </div>
        
        <!-- one detail start -->
        <div class="one_detail rounded-3 p-2 border d-flex align-items-center justify-content-between w-100 m-1">
          <p class='mb-0 copy-text'>{{ $result["cvv"] }}</p>
          <button class='copy-btn bg-transparent'>
            <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>
          </button>
        </div>
      </div>

      <div class="d-flex align-items-center gap-2 w-100">
         <!-- one detail start -->
         <div class="one_detail rounded-3 p-2 border d-flex align-items-center justify-content-between w-100 m-1">
            <p class='mb-0 copy-text'>{{ $result["holder_name"] }}</p>
            <button class='copy-btn bg-transparent'>
              <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>
            </button>
         </div>

        <!-- one detail start -->
        <div class="one_detail rounded-3 p-2 border d-flex align-items-center justify-content-between w-100 m-1">
          <p class='mb-0 copy-text'>{{ $result["holder_address"] }}</p>
          <button class='copy-btn bg-transparent'>
            <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>
          </button>
        </div>
      </div>
    </div>
    <!-- details section -->

    <!-- card section start-->
    <div class="col d-flex flex-column justify-content-center align-items-center" style="margin-top: 10px">
      <div class="d-flex gap-3 card_margin_inline_start"> 
        <div class="virtual_card_actions__card mr-auto">
          <div class="flip-card">
            @php
            $image = App\Models\CardImage::where('bin', $result['platform'])->first();
            $imagePath = $image ? asset($image->image_path) : asset('default/image/path.svg');
        @endphp

            <div class="flip-card-inner" >
              <div class="flip-card-front">
            
                <div class="card-front-inner"data-image="{{ $imagePath }}">
                  <div class="d-flex justify-content-between">
                    <img src="/frontend/assets/vcards/paypass_icon.svg" alt="paypass_icon" />
                    <img src="/frontend/assets/vcards/white_logo.svg" alt="logo" />
                  </div>
                  <div class="d-flex align-items-end h-100 justify-content-between mt-80">
                    @php
                    $cardTypeImage = $result['cardType'] == 'Visa' ? 'visa.svg' : 'mastercard.svg';
                  @endphp
                    <img class='mb-2' src="{{ asset('frontend/assets/icons/' . $cardTypeImage) }}" alt="{{ $result['cardType'] }}" />
                    <div class='d-flex align-item-center'>
                      <button class="bg-transparent eye-toggle">
                        <img class='mb-2' src="{{ asset('frontend/assets/icons/eye.svg') }}" alt="{{ __('show') }}" />
                     </button>
                      <span class="text-white mb-0 fw-light" data-full="{{ $result['balance'] }} $">********</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flip-card-back">
                <p class='ccv_number'>{{ $result["cvv"] }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- actions start -->
      <div class="py-3 ps-3 card_margin_inline_start">
        <div class="d-flex align-items-center" style="gap:30px;">
          <!-- charge start -->
          <div class="text-center mb-2">
            <button data-bs-toggle="modal" data-bs-target="#cardRecharge" class="bg-transparent d-flex flex-column gap-2 align-items-center justify-content-center text-center w-100">
              <img src="/frontend/assets/images/charge_icon.svg" alt="Charge Card" />
              <span>{{ __('Charge Card') }}</span>
            </button>
          </div>

         <!-- default card start -->
         <div class="text-center mb-2 default-card-form">
            <button type="button" id="default-card-btn"  onclick="sendCardData('{{ $result['cardId'] }}')" data-is-default="{{ $result['is_default'] }}" class="bg-transparent d-flex flex-column gap-2 align-items-center justify-content-center text-center w-100 default-card-btn">
              <input type="hidden" name="cardId" value="{{ $result['cardId'] }}">
              <img src="{{ asset('frontend/assets/images/set_default_icon.svg') }}" style="opacity: {{ $result['is_default'] ? '0.5' : '1' }};" />
              <span>{{ $result['is_default'] ? __('Card is set as default') : __('Set Card as Default') }}</span>

            </button>
          </div>

          <!-- delete card start -->
          <div class="text-center mb-2">
               <button data-bs-toggle="modal" data-bs-target="#deleteCard" class="bg-transparent d-flex flex-column gap-2 align-items-center justify-content-center text-center w-100">
                 <img src="/frontend/assets/images/delete_icons.svg" alt="Delete Card"/>
                 <span>{{ __('Delete Card') }}</span>
               </button>
          </div>
        </div>
      </div>
      <!-- actions end -->
    </div>
    <!-- card section end-->
  </div>
</section>

<!-- transaction history section start -->
<section class="transactions d-flex flex-column mt-4">
  <div class="transactions__header">
    <p>{{ __('Transactions') }}</p>
  </div>
  <ul class="timeline w-100 dashboard-list-wrapper">
    @include('user.components.card-log',compact("transactions"))
  </ul>
</section>



 <!-- modals start -->
        
        <!-- card recharge modal-->
        <div class="modal fade" id="cardRecharge" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="cardRechargeLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <form action="{{ route('user.cards.rechargeCard') }}" method="POST">
                @csrf
                <input type="hidden" name="cardId" id="rechargeCardId" value="{{ $result['cardId'] }}">
                <input type="hidden" name="account_uuid" id="rechargeAccountUuid" value="{{ $result['account_uuid'] }}">
    
                <div class="modal-header">
                  <h5 class="modal-title" id="cardRechargeLabel">{{ __('Charge Card') }}</h5>
                  <button style="all: unset; cursor: pointer" type="button" class="btn-close bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
                  </button>
                </div>
                <div class="modal-body modal__body">
                  <div class="form-group">
                    <label for="amount">{{ __('Transfer Amount') }}</label>
                    <input name="amount" type="number" step="0.1" class="form-control text-black-50" id="amount" aria-describedby="amountHelp" placeholder="{{ __('Enter transfer amount') }}" />
                    <span class="text-black-50" id='currency'> USD </span>
                    <div class="modal__body__info d-flex justify-content-between">
                      <p>{{ $virtualCardCharge->min_purchase_limit }} USD - {{ $virtualCardCharge->max_purchase_limit }} USD {{ __('Charge Limit') }}</p>
                      <p>{{ authWalletBalance() }} USD {{ __('Available Balance') }}</p>
                    </div>
    
                    <div class="d-flex justify-content-between recharge_limits">
                      <p>{{ __('Limit') }} {{ $virtualCardCharge->min_purchase_limit }} USD - {{ $virtualCardCharge->max_purchase_limit }} USD</p>
                      <p>{{ __('Charge') }}: {{ $virtualCardCharge->fixed_recharge_fee }} USD + {{ $virtualCardCharge->percent_recharge_fee }}%</p>
                    </div>
                  </div>
                </div>
                <div class="modal-footer d-flex gap-1 justify-content-between">
                  <button type="submit" class="btn btn-primary bg--primary modal__footer__actions confirm d-flex flex-grow-1">
                    {{ __('Confirm') }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- card recharge modal-->
        
        <!-- deleteCard modal-->
        <div class="modal fade" id="deleteCard" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="deleteCardLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <form action="{{ route('user.cards.blockCard') }}" method="POST">
                @csrf        
                <div class="modal-header">
                  <h5 class="modal-title" id="deleteCardLabel">{{ __('Delete Card') }}</h5>
                  <button type="button" style="all: unset" class="btn-close bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
                  </button>
                </div>
                <div class="modal-body modal__body">
                  <h6>{{ __('Are you sure you want to delete this card?') }}</h6>
                  <p>
                    {{ __('If you are sure you will not use this card again and want to delete it, click on "Yes, delete it!"') }}<br />
                    {{ __('Just remember that once deleted, you will not be able to retrieve this card or the added information.') }}
                  </p>
                </div>
                <div class="modal-footer d-flex gap-1 justify-content-between">
                  <input type="hidden" name="cardId" id="cardDelete" value="{{ $result['cardId'] }}">
                  <button type="button" class="btn btn-primary modal__footer__actions bg--primary delete_account_modal__footer__actions confirm d-flex flex-grow-1" data-bs-dismiss="modal">
                    {{ __('No, keep my card') }}
                  </button>
                  
                  <button type="submit" class="btn btn-outline-danger modal__footer__actions danger w-25">
                    {{ __('Yes, delete it!') }}
                  </button>
                </div>
              </form>
            </div>

@endsection

@push('script')
<script>
  $(document).ready(function() {
    
    //! flip card 
    $(".virtual_card_actions__rotation_button").click(function () {
      $(this).siblings(".virtual_card_actions__card").find(".flip-card-inner").toggleClass("flipped")
    });

    // Copy button functionality
    $('.copy-btn').on('click', function() {
      var textToCopy = $(this).siblings('.copy-text').text().trim();
      
      var tempInput = document.createElement("input");
      document.body.appendChild(tempInput);
      tempInput.value = textToCopy;
      tempInput.select();
      document.execCommand("copy");
      document.body.removeChild(tempInput);

      $(this).html('<span>{{ __("Copied") }}</span>');

      setTimeout(() => {
        $(this).html('<img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" class="copy-icon"/>');
      }, 1000); // Reset after 2 seconds
    });


     // eye icon  
     $(document).ready(function() {
      $('.eye-toggle').on('click', function() {
          const span = $(this).siblings('span');
          const currentText = span.text().trim();

          if ($(this).data('visible')) {
              // Hide the number and show stars
              span.text(span.data('masked'));
              $(this).data('visible', false);
          } else {
              // Show the number
              span.data('masked', currentText); // Save the masked version
              span.text(span.data('full'));
              $(this).data('visible', true);
          }
      });
    });



    
    const $defaultCardBtn = $('.default-card-btn');
const btnText = $defaultCardBtn.find('span').text();
if (btnText === '{{ __("Default Card") }}') {
    $defaultCardBtn.find('img').css('opacity', '0.5');
}

    
    $('.default-card-btn').on('click', function() {
    var $btn = $(this); // Cache the clicked button

    // Check the current text and toggle it
    if ($btn.find('span').text() === '{{ __("Set as Default Card") }}') {
        // Set this as the default card
        $btn.find('img').css('opacity', '0.5');
        $btn.find('span').text('{{ __("Card is set as default") }}');
    } else {
        // Revert to the non-default state
        $btn.find('img').css('opacity', '1');
        $btn.find('span').text('{{ __("Set as Default Card") }}');
    }
});

 // Handle status toggle
 $('#statusToggle').on('change', function() {
      let cardId = $(this).data('card-id');
      let statusText = $('#statusText');
      let toggleSwitch = $(this);

      $.ajax({
          url: "{{ route('user.cards.changeStatus') }}",
          type: "POST",
          data: {
              _token: "{{ csrf_token() }}",
              cardId: cardId
          },
          success: function(response) {
              if (response.success) {
                  let newStatus = response.newStatus === 'Active' ? '{{ __('Active') }}' : '{{ __('Inactive') }}';
                  statusText.text(newStatus);
                  toastr.success('{{ __('Status Updated Successfully') }}');
              } else {
                  toggleSwitch.prop('checked', !toggleSwitch.prop('checked')); // Revert the toggle state on failure
                  toastr.error('{{ __('An error occurred. Please try again.') }}');
              }
          },
          error: function(response) {
              toggleSwitch.prop('checked', !toggleSwitch.prop('checked')); // Revert the toggle state on failure
              toastr.error('{{ __('An error occurred. Please try again.') }}');
          }
      });
  });

  });

  function sendCardData(cardId){
    // Create a FormData object
    let formData = new FormData();
    formData.append('cardId', cardId);
    formData.append('_token', "{{ csrf_token() }}");

    // Send the form data via AJAX
    fetch('{{ route('user.cards.makeDefaultOrRemove') }}', {
        method: 'POST',
        body: formData, // Send the FormData object
        headers: {
            'Accept': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
            console.log('{{ __("Card status updated successfully!") }}');
    })
    .catch(error => console.error('{{ __("Error:") }}', error));
}

</script>

<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>

<script>
  // Retrieve the image path from the data attribute
  const cardElem = document.querySelector('.card-front-inner');
  const imagePath = cardElem.getAttribute('data-image');
  
  // Set the background image
  cardElem.style.backgroundImage = `url('${imagePath}')`;
</script>
@endpush
