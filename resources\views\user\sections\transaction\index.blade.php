@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />

@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __("Transactions")])
@endsection

@section('content')

<style>

  .custom-input input{
    min-width: 200px
  }
</style>

<section class="transactions d-flex flex-column">

    <div class="transactions__header custom-input">
      <p>سجل المعاملات</p>
      <input

        type="text"
        class="form-control"
        name="search"
        placeholder="البحث"
        
    />
</div>
<ul class=" w-100 ">

    @include('user.components.transaction-log-main',compact("transactions"))
    </ul>
    <!-- End -->
  </section>
  
@endsection

@push('script')
    <script>

var searchURL = "{{ setRoute('user.transactions.search') }}";
var timeOut;

$("input[name=search]").on("keyup", function() {
    clearTimeout(timeOut);
    timeOut = setTimeout(executeLogSearch, 500, $(this));
});

function executeLogSearch(input) {
    var searchText = input.val().trim();

    if (searchText.length === 0) {
        $(".search-result-item-wrapper").remove();
        $(".item-wrapper").removeClass("d-none");
        return;
    }

    $.ajax({
        url: searchURL,
        type: 'POST',
        data: {
            _token: laravelCsrf(),
            text: searchText,
        },
        success: function(response) {
            $(".search-result-item-wrapper").remove();
            $(".item-wrapper").addClass("d-none");

            if (response.error) {
                console.error(response.error);
                return;
            }

            $(".dashboard-list-wrapper").append(`
                <div class="search-result-item-wrapper">
                    ${response}
                </div>
            `);
        },
        error: function(xhr) {
            console.error(__("Something went wrong! Please try again."));
        }
    });
}

    </script>
    
    <script>
        let page = 1;
        $('#load-more-button').on('click', function() {
            page++;
            loadMoreData(page);
        });
    
        function loadMoreData(page) {
            $.ajax({
                url: '?page=' + page,
                type: 'get',
                beforeSend: function() {
                    $('#load-more-button').text('Loading...');
                }
            }).done(function(data) {
                if (data.html == "") {
                    $('#load-more-button').hide();
                    return;
                }
                $('#transactions-container').append(data);
                $('#load-more-button').text('Load More');
                if (page >= {{ $transactions->lastPage() }}) {
                    $('#load-more-button').hide();
                }
            }).fail(function(jqXHR, ajaxOptions, thrownError) {

            });
        }
    </script>
    
@endpush
