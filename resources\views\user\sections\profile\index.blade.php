@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/profile.css') }}" />
<link
rel="stylesheet"
href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css"
integrity="sha384-gXt9imSW0VcJVHezoNQsP+TNrjYXoGcrqBZJpry9zJt8PCQjobwmhMGaDHTASo9N"
crossorigin="anonymous"
/>
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')


<div class="container d-flex flex-column h-100">
  <form class="profile_component row" onsubmit="submitHandler(event)" action="{{ route('user.profile.update') }}" method="POST" enctype="multipart/form-data">
    @method('PUT')
    @csrf
    <div class="profile_component__avatar_box col-sm-12 col-lg-5">
        <img
            class="profile_component__avatar_box__avatar"
            src="{{ $user->userImage ? $user->userImage : asset('frontend/assets/images/userAvatarPlaceholder.svg') }}"
        />
        <label for="formFile" class="form-label">
            <img
                class="profile_component__avatar_box__avatar__upload_icon"
                src="{{ asset('frontend/assets/images/uploadImageIcon.svg') }}"
            />
        </label>
        <input required class="form-control d-none" type="file" accept='image/*' id="formFile" name="image"/>
        <p class="profile_component__avatar_box__avatar__text">{{ $user->fullname }}</p>
    </div>
    <div class="profile_component__delete col-sm-12 col-lg-5">
        <button
            class="d-flex gap-2 align-self-end"
            style="cursor: pointer; background-color: transparent"
            type="button"
            data-bs-toggle="modal"
            data-bs-target="#deleteAccount"
        >
            <img src="{{ asset('frontend/assets/images/delete.svg') }}" height="20" width="20" />
            <p class="profile_component__avatar_box__delete_account">{{ __('Delete Account') }}</p>
        </button>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="firstname">{{ __('First Name') }} </label>
        <input required
            type="text"
            class="form-control"
            id="firstname"
            name="firstname"
            value="{{ $user->firstname }}"
            aria-describedby="firstnameHelp"
            placeholder="{{ __('Enter First Name') }}"
        />
        <small id="firstnameHelp" class="form-text text-danger form-text-danger m-0">{{ __('First Name is required') }}</small>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="lastname">{{ __('Last Name') }}</label>
        <input required
            type="text"
            class="form-control"
            id="lastname"
            name="lastname"
            value="{{ $user->lastname }}"
            aria-describedby="lastnameHelp"
            placeholder="{{ __('Enter Last Name') }}"
        />
        <small id="lastnameHelp" class="form-text text-danger form-text-danger m-0">{{ __('Last Name is required') }}</small>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="email">{{ __('Email') }}</label>
        <input required
            type="email"
            class="form-control"
            id="email"
            name="email"
            value="{{ $user->email }}"
            aria-describedby="emailHelp"
            placeholder="{{ __('Enter your email') }}"
        />
        <small id="emailHelp" class="form-text text-danger form-text-danger m-0">{{ __('Email is required') }}</small>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="country">{{ __('Country') }}</label>
        <select class="form-control select-input" id="country" name="country">
            <option selected disabled style="background-color: gray">{{ __('Select Country') }}</option>
            @foreach(trans('countries') as $key => $value)
                <option value="{{ $key }}" {{ $user->address->country == $key ? 'selected' : '' }}>{{ $value }}</option>
            @endforeach
        </select>
        <small id="countryHelp" class="form-text text-danger form-text-danger m-0">{{ __('Country is required') }}</small>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="state">{{ __('Province or State') }}</label>
        <input 
            type="text"
            class="form-control"
            id="state"
            name="state"
            value="{{ $user->address->state }}"
            aria-describedby="stateHelp"
            placeholder="{{ __('Enter Province or State') }}"
        />
        <small id="stateHelp" class="form-text text-danger form-text-danger m-0">{{ __('Province or State is required') }}</small>
    </div>
    <div class="form-group col-sm-12 col-lg-6">
        <label for="city">{{ __('City') }}</label>
        <input
            type="text"
            class="form-control"
            id="city"
            name="city"
            value="{{ $user->address->city }}"
            aria-describedby="cityHelp"
            placeholder="{{ __('Enter City') }}"
        />
        <small id="cityHelp" class="form-text text-danger form-text-danger m-0">{{ __('City is required') }}</small>
    </div>
    <button
        data-bs-toggle="modal"
        data-bs-target="#resetPassword"
        class="profile_component__reset_password_btn"
        type="button"
    >
        <img src="{{ asset('frontend/assets/images/lock.svg') }}" height="21" width="18" />
        <p class="profile_component__reset_password_btn__text">{{ __('Change Password') }}</p>
    </button>

    <button type="submit" class="profile_component__submit_btn col-12">
        {{ __('Save Changes') }}
    </button>
</form>

</div>
<!-- reset-password-modal -->
<div
    class="modal fade"
    id="resetPassword"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="resetPasswordLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
        <form action="{{ setRoute('user.profile.password.update') }}" method="POST" role="form" id="passwordChangeForm">
    @method("PUT")
    @csrf
    <div class="modal-header">
        <h5 class="modal-title" id="resetPasswordLabel">{{ __('Change Password') }}</h5>
        <button
            type="button"
            class="btn-close bg-transparent"
            data-bs-dismiss="modal"
            aria-label="Close"
        >
            <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
        </button>
    </div>
    <div class="modal-body">
        <div class="form-group show_hide_password">
            <label for="currentPassword">{{ __('Current Password') }}</label>
            <input required
                name="current_password"
                type="password"
                class="form-control"
                id="currentPassword"
                aria-describedby="currentPasswordHelp"
                placeholder="{{ __('Enter Current Password') }}"
            />
            <a href="javascript:void(0)" class="show-pass"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
            <!-- <button type="button" class="reset_password_modal__eye_icon">
                <img alt="eye" src="{{ asset('frontend/assets/images/eye.svg') }}" />
            </button> -->
            <small id="currentPasswordHelp" class="form-text text-danger form-text-danger m-0">{{ __('Current Password is required') }}</small>
        </div>
        <div class="form-group show_hide_password_new">
            <label for="newPassword">{{ __('New Password') }}</label>
            <input required
                name="password"
                type="password"
                class="form-control"
                id="newPassword"
                aria-describedby="newPasswordHelp"
                placeholder="{{ __('Enter New Password') }}"
            />
            <a href="javascript:void(0)" class="show-pass">
                <i class="fa fa-eye-slash" aria-hidden="true"></i>
            </a>
            <small id="newPasswordHelp" class="form-text text-danger form-text-danger m-0">{{ __('New Password is required') }}</small>
        </div>

        <div class="form-group show_hide_password_confirm">
            <label for="newPasswordConfirm">{{ __('Confirm New Password') }}</label>
            <input required
                name="password_confirmation"
                type="password"
                class="form-control"
                id="newPasswordConfirm"
                aria-describedby="newPasswordConfirmHelp"
                placeholder="{{ __('Confirm New Password') }}"
            />
            <a href="javascript:void(0)" class="show-pass"><i class="fa fa-eye-slash" aria-hidden="true"></i></a>
            <!-- <button type="button" class="reset_password_modal__eye_icon">
                <img alt="eye" src="{{ asset('frontend/assets/images/eye.svg') }}" />
            </button> -->
            <small id="newPasswordConfirmHelp" class="form-text text-danger form-text-danger m-0">{{ __('Confirm New Password is required') }}</small>
        </div>
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-primary w-100">{{ __('Confirm') }}</button>
    </div>
</form>
        </div>
    </div>
</div>
<!-- end of reset-password-modal -->
      
<!-- delete-account-modal -->
<div
    class="modal fade"
    id="deleteAccount"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="deleteAccountLabel"
    aria-hidden="true"
>
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form action="{{ route('user.delete.account') }}" method="POST">
                @csrf
                @method('DELETE')
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAccountLabel">{{ __('Delete Account') }}</h5>
                    <button
                        type="button"
                        class="btn-close bg-transparent"
                        data-bs-dismiss="modal"
                        aria-label="Close"
                    >
                        <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
                    </button>
                </div>
                <div class="modal-body delete_account_modal__body">
                    <h6>{{ __('Are you sure you want to delete your account?') }}</h6>
                    <p>
                        {{ __('If you are sure you will not use your account again and want to delete it, click "Yes, delete it!". Just remember that once deleted, you will not be able to recover this account or any added information.') }}
                    </p>
                </div>
                <div class="modal-footer d-flex gap-1 justify-content-between">
                    <button
                        type="button"
                        class="btn btn-primary bg--primary delete_account_modal__footer__actions confirm d-flex flex-grow-1"
                        data-bs-dismiss="modal"
                    >
                        {{ __('No, I will keep my account') }}
                    </button>
                
                    <button
                        type="submit"
                        class="btn btn-outline-danger delete_account_modal__footer__actions danger w-25"
                    >
                        {{ __('Yes, delete it!') }}
                    </button>

                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('script')

    <script>
      $(".reset_password_modal__eye_icon").click(function () {
        const currentType = $(this).siblings(".form-control").attr("type")
        const newType = currentType === "text" ? "password" : "text"
        $(this).siblings(".form-control").attr("type", newType)
      })
  // Clear previous error messages
  document.querySelectorAll('.form-text-danger').forEach((element) => {
        element.style.display = 'none';
    });

    // ! update profile validation 
    // Get all form fields and help elements
const formFields = ['firstname', 'lastname', 'email', 'country'].reduce((acc, id) => {
    acc[id] = document.getElementById(id);
    return acc;
}, {});

const helpElements = ['firstname', 'lastname', 'email', 'country'].reduce((acc, id) => {
    acc[id] = document.getElementById(`${id}Help`);
    return acc;
}, {});

// Function to validate and show/hide help messages
const validateField = id => {
    const value = formFields[id].value.trim();
    helpElements[id].style.display = value ? 'none' : 'block';
    return !!value;
};

// Add event listeners to fields
Object.keys(formFields).forEach(id => {
    formFields[id].addEventListener(id === 'country' ? 'change' : 'input', () => validateField(id));
});

// Submit handler
function submitHandler(event) {
    event.preventDefault(); // Prevent the form from submitting

    const formIsValid = Object.keys(formFields).every(validateField);

    // Submit form if all fields are valid
    if (formIsValid) {
        event.target.submit();
    }
}

// Optional: Enable submit button when all fields are valid
function checkFormValidity() {
    submitButton.disabled = !Object.keys(formFields).every(id => formFields[id].value.trim());
}

Object.keys(formFields).forEach(id => {
    formFields[id].addEventListener('input', checkFormValidity);
    formFields[id].addEventListener('change', checkFormValidity);
});


//  !  change password validation 
document.addEventListener("DOMContentLoaded", () => {
    const formFields = {
        currentPassword: document.getElementById('currentPassword'),
        newPassword: document.getElementById('newPassword'),
        newPasswordConfirm: document.getElementById('newPasswordConfirm')
    };

    const helpElements = {
        currentPassword: document.getElementById('currentPasswordHelp'),
        newPassword: document.getElementById('newPasswordHelp'),
        newPasswordConfirm: document.getElementById('newPasswordConfirmHelp')
    };

    const validateField = id => {
        const value = formFields[id].value.trim();
        let isValid = true;
        helpElements[id].style.display = 'none';

        if (id === 'newPassword' || id === 'newPasswordConfirm') {
            // Validate length for newPassword and newPasswordConfirm
            if (value.length < 6) {
                helpElements[id].textContent = '{{ __("Password must be at least 6 characters long") }}';
                helpElements[id].style.display = 'block';
                isValid = false;
            }
        } else {
            // Validate currentPassword
            if (!value) {
                helpElements[id].style.display = 'block';
                isValid = false;
            }
        }

        return isValid;
    };

    // ! password validation
    const validatePasswordsMatch = () => {
        const newPassword = formFields.newPassword.value.trim();
        const newPasswordConfirm = formFields.newPasswordConfirm.value.trim();
        let isValid = true;

        if (newPassword !== newPasswordConfirm) {
            helpElements.newPasswordConfirm.textContent = '{{ __("Passwords do not match") }}';
            helpElements.newPasswordConfirm.style.display = 'block';
            isValid = false;
        } else {
            helpElements.newPasswordConfirm.style.display = 'none';
        }

        return isValid;
    };

    Object.keys(formFields).forEach(id => {
        formFields[id].addEventListener('input', () => {
            validateField(id);
            if (id === 'newPassword' || id === 'newPasswordConfirm') {
                validatePasswordsMatch();
            }
        });
    });

    document.getElementById('passwordChangeForm').addEventListener('submit', event => {
        event.preventDefault();
        const formIsValid = Object.keys(formFields).every(validateField) && validatePasswordsMatch();

        if (formIsValid) {
            event.target.submit();
        }
    });
});

// ! upload photo 

const inputFile = $('#formFile');
const profileAvatar = $('.profile_component__avatar_box__avatar');

inputFile.on('change', function(event) {
    const file = event.target.files[0];
    if (!file) return;
    profileAvatar.attr('src', URL.createObjectURL(file));
});


    </script>
@endpush