@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb', ['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')
<div class="px-4">
<!-- Cards -->
<div class="container dash mb-4">
  <h2 class="fw-bold d-none d-md-block">{{ __('welcome_back') }}, <span>{{ auth()->user()->fullname }}</span> 
    <img src="{{ asset('frontend/assets/images/hand.svg') }}" alt="{{ __('waving_hand') }}">
  </h2>
  <div class="row row-gap-4">
    <div class="col-sm-12 col-md-6 col-lg-3">
      <div class="card mb-2" style="width: 100%">
        <div class="card-body d-flex flex-column gap-3">
          <h6 class="card-subtitle text-muted">{{ __('current_balance') }}</h6>
          <div class="d-flex gap-2 align-items-center">
            <img src="{{ asset('frontend/assets/icons/wallet.svg') }}" alt="{{ __('wallet') }}" />
            <h5 class="card-title">{{ round($balance,3) }}$</h5>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-12 col-md-6 col-lg-3">
      <div class="card mb-2" style="width: 100%">
        <div class="card-body d-flex flex-column gap-3">
          <h6 class="card-subtitle text-muted">{{ __('total_deposited_money') }}</h6>
          <div class="d-flex gap-2 align-items-center">
            <img src="{{ asset('frontend/assets/icons/deposit.svg') }}" alt="{{ __('deposit') }}" />
            <h5 class="card-title">{{ round($totalAddMoney,3) }}$</h5>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-12 col-md-6 col-lg-3">
      <div class="card mb-2" style="width: 100%">
        <div class="card-body d-flex flex-column gap-3">
          <h6 class="card-subtitle text-muted">{{ __('active_transactions') }}</h6>
          <div class="d-flex gap-2 align-items-center">
            <img src="{{ asset('frontend/assets/icons/transactions.svg') }}" alt="{{ __('transactions') }}" />
            <h5 class="card-title">{{ $ActiveTransactions }}</h5>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-12 col-md-6 col-lg-3">
      <div class="card mb-2" style="width: 100%">
        <div class="card-body d-flex flex-column gap-3">
          <h6 class="card-subtitle text-muted">{{ __('active_cards') }}</h6>
          <div class="d-flex gap-2 align-items-center">
            <img src="{{ asset('frontend/assets/icons/card.svg') }}" alt="{{ __('card') }}" />
            <h5 class="card-title">{{ $activeCards }}</h5>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Cards -->

<!-- Transaction History Section Start -->
<section class="transactions d-flex flex-column custom-dash">
  <div class="transactions__header">
    <p>{{ __('latest_transactions') }}</p>
  </div>
  <ul class="timeline w-100">
    @foreach ($transactions as $item)
    @php
    // Determine the status text and class
    switch ($item->status) {
        case 1:
            $statusText = __('successful');
            $statusClass = '--success';
            break;
        case 2:
            $statusText = __('pending');
            $statusClass = '--pending';
            break;
        case 3:
            $statusText = __('on_hold');
            $statusClass = '--hold';
            break;
        case 4:
            $statusText = __('rejected');
            $statusClass = '--rejected';
            break;
        default:
            $statusText = __('failed');
            $statusClass = '--failed';
            break;
    }

    // Extract the part of the remark before "by"
    $remark = $item->remark;
    $position = strpos($remark, 'by');
    $remarkBeforeBy = $position !== false ? substr($remark, 0, $position) : $remark;

    $prefixes = ['TRANSFER MONEY From', 'TRANSFER MONEY To'];
    $translatedRemark = $remark;
    foreach ($prefixes as $prefix) {
        if (strpos($remarkBeforeBy, $prefix) === 0) {
            $translatedRemark = __($prefix) . substr($remarkBeforeBy, strlen($prefix));
            break;
        }
    }
    @endphp
    <li class="timeline-item transaction d-flex align-content-start gap-6">
      <div class="d-flex flex-column">
        <div class="transaction__icon">
          <img src="{{ asset('frontend/assets/images/' . $item->type . '.svg') }}" alt="{{ __('transaction-icon') }}" />
        </div>
      </div>
      <div class="transaction__card flex-grow-1">
        <div class="d-flex justify-content-between">
          <p class="text-dark fw-medium m-0">
            @if ($position)
                {{ __($remarkBeforeBy) }} <span class="text--primary">{{ substr($remark, $position + 3) }}</span>
            @else
                <span class="text--primary">{{ __($translatedRemark) }}</span>
            @endif
          </p>
          <p class="transaction__card__date text-dark fw-medium mb-0">
            {{ $item->created_at }}
          </p>
        </div>
        <div class="d-flex justify-content-between">
          <div class="transaction__card__status fw-bolder d-flex gap-1">
            <p class="transaction__card__status__icon {{ $statusClass }}"></p>
            <p class="transaction__card__status__text mb-0 fw-bold">{{ __($statusText) }}</p>
          </div>
          <p class="transaction__card__amount fw-medium text--primary mb-0">
            ${{ $item->payable }}
          </p>
        </div>
        <div class="transaction__card_details">
          <div class="d-flex justify-content-between">
            <span class="transaction__card_details__row d-flex gap-2 align-content-center">
              <img src="{{ asset('frontend/assets/images/tareget.svg') }}" />
              <p class="transaction__card_details__row__text">{{ __('transaction') }}</p>
            </span>
            <p class="transaction__card_details__row__text">{{ $item->trx_id }}</p>
          </div>
          <div class="d-flex justify-content-between">
            <span class="transaction__card_details__row d-flex gap-2 align-content-center">
              <img src="{{ asset('frontend/assets/images/bill.svg') }}" />
              <p class="transaction__card_details__row__text">{{ __('fees_and_charges') }}</p>
            </span>
            <p class="transaction__card_details__row__text">{{ $item->charge->total_charge ?? null }}</p>
          </div>
          <div class="d-flex justify-content-between">
            <span class="transaction__card_details__row d-flex gap-2 align-content-center">
              <img src="{{ asset('frontend/assets/images/purse.svg') }}" />
              <p class="transaction__card_details__row__text">{{ __('balance_after_transaction') }}</p>
            </span>
            <p class="transaction__card_details__row__text">{{ $item->available_balance }}</p>
          </div>
          <div class="d-flex justify-content-between">
            <span class="transaction__card_details__row d-flex gap-2 align-content-center">
              <img src="{{ asset('frontend/assets/images/clock.svg') }}" />
              <p class="transaction__card_details__row__text">{{ __('time_and_date') }}</p>
            </span>
            <p class="transaction__card_details__row__text">{{ $item->created_at }}</p>
          </div>

          @if($item->reject_reason)

          <div class="d-flex justify-content-between">
                  <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                      <img src="{{ asset('frontend/assets/icons/rejection_reason.svg') }}" />
                      <p class="transaction__card_details__row__text">
                         {{__("rejection reason")}}
                      </p>
                  </span>
                  <p class="transaction__card_details__row__text ">
                      {{$item->reject_reason}}
                  </p>
          </div>
          @endif
        </div>
      </div>
    </li>
    @endforeach
  </ul>
  <!-- End -->
</section>
</div>
@endsection

@push('script')
<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>
@endpush
