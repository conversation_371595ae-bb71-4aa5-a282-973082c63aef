@extends('frontend.layouts.master')
{{-- @php
    $lang = selectedLang();
    $banner_slug = Illuminate\Support\Str::slug(App\Constants\SiteSectionConst::HOME_BANNER);
    $banner = App\Models\Admin\SiteSections::getData( $banner_slug)->first();

@endphp --}}



@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/nice-select.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/hero.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/index.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/navbar.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/services_style.css') }}" />
@endpush

@section('content')
<section id="hero">
  <div id="heroHeader" class="d-flex flex-wrap">
    <!-- right section start -->
    <div id="right_section" class="w-50 d-flex flex-column align-items-start justify-content-center">
      <div id="purpleShadow"></div>
      <h1 class="hero__heading text--dark">
            {{ __('Get the Freedom of') }} <span>{{__('e-payment')}}</span><br /> {{ __('Without') }} &nbsp;
        <span class="color__primary span1">{{ __('Restrictions') }}</span>
        <span class="color__primary span2">{{ __('Bank account') }}</span>
        <span class="color__primary span3">{{ __('Fears') }}</span>
      </h1>
      <p class="text-black">
        {{ __('create_virtual_card_now') }}
      </p>
      <div class="d-flex align-items-center justify-content-center">
        @guest
          <button type="button" class="btn__primary rounded-3 text-white btn header-account-btn">
            {{ __('create_your_virtual_card') }}
          </button>
        @else
          <a  href="{{ setRoute('user.cards.index') }}" class="btn__primary rounded-3 text-white btn">
            {{ __('create_your_virtual_card') }}
          </a>
        @endguest
      </div>
    </div>
    <!-- right section end -->

    <!-- left section start -->
    <div id="left_section" class="w-50 d-flex justify-content-center align-items-center">
      <div id="blueShadow"></div>
      <img src="{{ asset('frontend/assets/hero.svg') }}" alt="{{ __('hero_image_alt') }}" />
    </div>
    <!-- left section end -->

    <!-- lines start-->
    <div id="coloredLines" dir='rtl' class="w-100 d-flex justify-content-between align-items-center">
      <!-- left lines start -->
      <div id="leftLines">
        <div class="left__blue__color"></div>
        <div class="left__purple__color"></div>
      </div>
      <!-- left lines end -->

      <!-- right lines start -->
      <div id="rightLines">
        <div class="right__purple__color"></div>
        <div class="right__blue__color"></div>
      </div>
      <!-- right lines end -->
    </div>
    <!-- lines end-->
  </div>
</section>

<section class="about bg-gray p-5">
  <div class="container d-flex align-items-center justify-content-center">
    <div class="row d-flex gap-lg-0 gap-3">
      <div class="col-md-6 d-flex flex-column gap-lg-3 align-items-md-start align-items-center gap-4">
        <span class="chip">{{ __('about_zaed') }}</span>
        <h1>{{ __('safe_online_solutions') }}</h1>
        <p class="mt-1">
          {{ __('zaed_provides_virtual_cards') }}
        </p>
        @auth
            @if(auth()->user()->email_verified == 0)
            <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
            @else
             <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{ __('Dashboard') }}</a>
            @endif

        @else
        <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
        @endauth
      </div>
      <div class="col-md-6">
        <div class="frame">
          <img height="538" width="600" src="{{ asset('frontend/assets/website_about_img.svg') }}" alt="{{ __('about_image_alt') }}" />
        </div>
      </div>
    </div>
  </div>
</section>

<section class="features bg-gray">
  <div class="container d-flex flex-column justify-content-center align-items-center gap-lg-2 gap-3">
    <div class="heading d-flex flex-column gap-2 justify-content-center align-items-center text-center">
      <span class="chip">{{ __('our_features') }}</span>
      <h1>{{ __('redefining_online_transactions') }}</h1>
      <p>
        {{ __('discover_features') }}
      </p>
    </div>
    <div class="row mt-4">
      <div class="col-md-3 col-12 mb-4">
        <div class="feat_card">
          <div class="icon_card">
            <img src="{{ asset('frontend/assets/images/CreditCard.svg') }}" alt="{{ __('credit_card_icon_alt') }}" />
          </div>
          <div class="feat_content d-flex flex-column gap-2 align-items-center justify-content-center">
            <h3>{{ __('virtual_credit_cards') }}</h3>
            <p>{{ __('say_goodbye_to_physical_credit_cards') }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-12 mb-4">
        <div class="feat_card">
          <div class="icon_card">
            <img src="{{ asset('frontend/assets/images/Secure.svg') }}" alt="{{ __('credit_card_icon_alt') }}" />
          </div>
          <div class="feat_content d-flex flex-column gap-2 align-items-center justify-content-center">
            <h3>{{ __('virtual_credit_cards') }}</h3>
            <p>{{ __('say_goodbye_to_physical_credit_cards') }}</p>
          </div>
        </div>
      </div>

      <div class="col-md-3 col-12 mb-4">
        <div class="feat_card">
          <div class="icon_card">
            <img src="{{ asset('frontend/assets/images/Vector.svg') }}" alt="{{ __('credit_card_icon_alt') }}" />
          </div>
          <div class="feat_content d-flex flex-column gap-2 align-items-center justify-content-center">
            <h3>{{ __('virtual_credit_cards') }}</h3>
            <p>{{ __('say_goodbye_to_physical_credit_cards') }}</p>
          </div>
        </div>
      </div>

      <div class="col-md-3 col-12 mb-4">
        <div class="feat_card">
          <div class="icon_card">
            <img src="{{ asset('frontend/assets/images/Sign.svg') }}" alt="{{ __('credit_card_icon_alt') }}" />
          </div>
          <div class="feat_content d-flex flex-column gap-2 align-items-center justify-content-center">
            <h3>{{ __('virtual_credit_cards') }}</h3>
            <p>{{ __('say_goodbye_to_physical_credit_cards') }}</p>
          </div>
        </div>
      </div>
      <!-- Repeat for other feature cards -->
    </div>
  </div>
</section>

<section class="how-work bg-gray">
  <div class="container">
    <div class="row">
      <div class="col-md-6">
        <div class="frame">
          <img src="{{ asset('frontend/assets/how_work_image.png') }}" alt="{{ __('how_we_work_image_alt') }}" />
        </div>
      </div>
      <div class="col-md-6 d-flex flex-column gap-3 align-items-start">
        <span class="chip">{{ __('how_we_work') }}</span>
        <h3>{{ __('how_we_work_question') }}</h3>
        <ul class="d-flex flex-column gap-4">
          <li>
            <div class="how_we_work_step d-flex align-items-start gap-3">
              <div class="how_we_work_step__image_box">
                <img src="{{ asset('frontend/assets/how_we_work_folder.svg') }}" alt="{{ __('load_money_icon_alt') }}">
              </div>
              <div class="how_we_work_step__content d-flex flex-column gap-2">
                <h3>{{ __('load_money') }}</h3>
                <span>{{ __('load_money_desc') }}</span>
              </div>
            </div>
          </li>
          <li>
            <div class="how_we_work_step d-flex align-items-start gap-3">
              <div class="how_we_work_step__image_box">
                <img src="{{ asset('frontend/assets/cash.svg') }}" alt="{{ __('subscribe_icon_alt') }}">
              </div>
              <div class="how_we_work_step__content d-flex flex-column gap-2">
                <h3>{{ __('subscribe_to_zaed') }}</h3>
                <span>{{ __('subscribe_to_zaed_desc') }}</span>
              </div>
            </div>
          </li>
          <li>
            <div class="how_we_work_step d-flex align-items-start gap-3">
              <div class="how_we_work_step__image_box">
                <img src="{{ asset('frontend/assets/money.svg') }}" alt="{{ __('apply_virtual_card_icon_alt') }}">
              </div>
              <div class="how_we_work_step__content d-flex flex-column gap-2">
                <h3>{{ __('apply_virtual_card') }}</h3>
                <span>{{ __('apply_virtual_card_desc') }}</span>
              </div>
            </div>
          </li>
        </ul>
        <a href="/" class="d-flex gap-3 how-work__icon align-items-center">
          <span class="play-icon">
            <img src="{{ asset('frontend/assets/play_icon.svg') }}" alt="{{ __('play_icon_alt') }}">
          </span>
          <p>{{ __('watch_how_we_work') }}</p>
        </a>
      </div>
    </div>
  </div>
</section>

<section class="services bg-gray p-4">
  <div class="container d-flex flex-column gap-5 justify-content-center align-items-center">
    <div class="heading d-flex flex-column gap-2 align-items-center">
      <span class="chip">{{ __('our_top_services') }}</span>
      <h3>{{ __('feel_safe_with_zaed_smart_solutions') }}</h3>
      <p>{{ __('experience_efficient_services') }}</p>
    </div>
    <div class="boxes__wrapper gap-lg-5 gap-0 services bg-gray justify-content-center py-lg-4 p-0">
      <div class="d-flex flex-column gap-2 column__wrapper">
        <div style="background-color: #6860FF" class="box1 box__base service-card">
          <div class="d-flex text-end flex-column">
            <div class="card-content">
              <h4>{{ __('subscribe_to_zaed') }}</h4>
              <p>{{ __('lorem_paragraph') }}</p>
            </div>
            <div style="background-color: #6860FF" class="card_icon">
              <img src="{{ asset('frontend/assets/note.svg') }}" alt="{{ __('note_icon_alt') }}">
            </div>
          </div>
        </div>

        <div style="background-color: #07C683" class="box2 box__base service-card">
          <div class="d-flex text-end flex-column">
            <div class="card-content">
              <h4>{{ __('subscribe_to_zaed') }}</h4>
              <p>{{ __('lorem_paragraph') }}</p>
            </div>
            <div style="background-color: #07C683" class="card_icon">
              <img src="{{ asset('frontend/assets/Safe-Box.svg') }}" alt="{{ __('safe_box_icon_alt') }}">
            </div>
          </div>
        </div>
      </div>

      <div class="d-flex flex-column gap-2 column__wrapper">
        <div style="background-color: #E9034A" class="box3 box__base service-card">
          <div class="d-flex text-end flex-column">
            <div class="card-content">
              <h4>{{ __('subscribe_to_zaed') }}</h4>
              <p>{{ __('lorem_paragraph') }}</p>
            </div>
            <div style="background-color: #E9034A" class="card_icon">
              <img src="{{ asset('frontend/assets/Smartphone.svg') }}" alt="{{ __('smartphone_icon_alt') }}">
            </div>
          </div>
        </div>

        <div style="background-color: #3DA7FC" class="box4 box__base service-card">
          <div class="d-flex text-end flex-column">
            <div class="card-content">
              <h4>{{ __('subscribe_to_zaed') }}</h4>
              <p>{{ __('lorem_paragraph') }}</p>
            </div>
            <div style="background-color: #3DA7FC" class="card_icon">
              <img src="{{ asset('frontend/assets/Money-Coin.svg') }}" alt="{{ __('money_coin_icon_alt') }}">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="statistics">
  <div class="container justify-content-evenly">
    <div class="row">
      <div class="col-md-5">
        <h3>{{ __('trust_us_like_thousands') }}</h3>
      </div>
      <div class="col-md-7 d-flex flex-column gap-5 justify-content-between">
        <p>{{ __('discover_freedom_with_zaed') }}</p>
        <div class="d-flex flex-lg-row flex-column statistics_items">
          <div>
            <h3>2500K</h3>
            <span>{{ __('clients') }}</span>
          </div>
          <div>
            <h3>2500K</h3>
            <span>{{ __('clients') }}</span>
          </div>
          <div>
            <h3>2500K</h3>
            <span>{{ __('clients') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="faq bg-gray">
  <div class="container">
    <div class="heading d-flex flex-column gap-2 align-items-center">
      <span class="chip">{{ __('frequently_asked_questions') }}</span>
      <h3>{{ __('feel_safe_with_zaed_smart_solutions') }}</h3>
      <p>{{ __('experience_efficient_services') }}</p>
    </div>
    <div class="accordion" id="accordionExample">
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="true" aria-controls="collapseOne">
            {{ __('how_to_create_virtual_card') }}
            <img class="rotate arrow" src="{{ asset('frontend/assets/down-arrow.svg') }}" />
          </button>
        </h2>
        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            {{ __('lorem_text') }}
          </div>
        </div>
      </div>
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingOne">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
            {{ __('how_to_create_virtual_card') }}
            <img class="rotate arrow" src="{{ asset('frontend/assets/down-arrow.svg') }}" />
          </button>
        </h2>
        <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            {{ __('lorem_text') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="testimonials bg-gray py-5">
  <div class="container d-flex flex-column gap-4">
    <div class="heading">
      <span class="chip">{{ __('client_testimonials') }}</span>
    </div>
    <div id="carouselExampleDark" class="carousel carousel-dark slide">
      <div class="carousel-indicators">
        <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
        <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="1" aria-label="Slide 2"></button>
        <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="2" aria-label="Slide 3"></button>
      </div>

      <div class="carousel-inner">

        <div class="carousel-item active">
          <div class="d-flex flex-lg-row flex-column gap-2 justify-content-between">
            <div class="user_info d-flex gap-3">
              <img src="{{ asset('frontend/assets/client.svg') }}" alt="{{ __('client_icon_alt') }}">
              <span class="d-flex flex-column">
                <h4>Tarek Allam</h4>
                <p>CEO & Founder at mija</p>
              </span>
            </div>
            <div class="content w-50 carousel__content">
              <div id="qoute-top">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              <div id="qoute-down">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              {{ __('good_idea_to_pay_with_zaed') }}
            </div>
          </div>
        </div>

        <div class="carousel-item">
          <div class="d-flex flex-lg-row flex-column gap-2 justify-content-between">
            <div class="user_info d-flex gap-3">
              <img src="{{ asset('frontend/assets/client.svg') }}" alt="{{ __('client_icon_alt') }}">
              <span class="d-flex flex-column">
                <h4>Nicola Tesla</h4>
                <p>Scientist</p>
              </span>
            </div>
            <div class="content w-50 carousel__content">
              <div id="qoute-top">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              <div id="qoute-down">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              {{ __('nicola_tesla_quote') }}
            </div>
          </div>
        </div>

        <div class="carousel-item">
          <div class="d-flex flex-lg-row flex-column gap-2 justify-content-between">
            <div class="user_info d-flex gap-3">
              <img src="{{ asset('frontend/assets/client.svg') }}" alt="{{ __('client_icon_alt') }}">
              <span class="d-flex flex-column">
                <h4>Leo Messi</h4>
                <p>Soccer & The GOAT</p>
              </span>
            </div>
            <div class="content w-50 carousel__content">
              <div id="qoute-top">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              <div id="qoute-down">
                <img src="{{ asset('frontend/assets/qouts.svg') }}" alt="{{ __('quote_icon_alt') }}">
              </div>
              {{ __('leo_messi_quote') }}
            </div>
          </div>
        </div>

      </div>
      <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">{{ __('previous') }}</span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">{{ __('next') }}</span>
      </button>
    </div>
  </div>
</section>

<section class="start p-3">
  <div class="container">
    <div class="box">
      <h1>{{ __('ready_to_join_zaed') }}</h1>

        @auth
            @if(auth()->user()->email_verified == 0)
            <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
            @else
             <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{ __('Dashboard') }}</a>
            @endif

        @else
        <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
        @endauth
      
      <img src="{{ asset('frontend/assets/logo-img.svg') }}" alt="{{ __('logo_image_alt') }}">
    </div>
  </div>
</section>


@endsection

@push("script")
<script src="{{ asset('frontend/js/services-component-core.js') }}"></script>

<script>

    $(document).ready(function () {
      $(".accordion-header").click(function () {
        $(this).find(".arrow")
                .toggleClass("rotate")
      })
    })

    $(document).ready(function () {
      const $spans = $(".color__primary");

    let currentIndex = 0;

    function showNextSpan() {
        // Hide all spans
        $spans.removeClass("active");

        // Move to the next span
        currentIndex = (currentIndex + 1) % $spans.length;

        // Show the current span
        $spans.eq(currentIndex).addClass("active");

        // Schedule the next change
        setTimeout(showNextSpan, 1000);
    }

    // Schedule the first change
    setTimeout(showNextSpan, 0);
  });
  </script>




@endpush
