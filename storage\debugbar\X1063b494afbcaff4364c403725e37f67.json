{"__meta": {"id": "X1063b494afbcaff4364c403725e37f67", "datetime": "2025-06-22 15:17:34", "utime": **********.930439, "method": "GET", "uri": "/user/add-money", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:17:34] LOG.error: Attempt to read property \"fixed_charge\" on null {\n    \"view\": {\n        \"view\": \"D:\\\\projects\\\\CardApp\\\\resources\\\\views\\\\user\\\\sections\\\\add-money\\\\index.blade.php\",\n        \"data\": {\n            \"basic_settings\": \"<pre class=sf-dump id=sf-dump-843815548 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\Admin\\\\BasicSettings<\\/span> {<a class=sf-dump-ref>#1362<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">basic_settings<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:31<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>site_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">CardApp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_title<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\"> Virtual Credit Card Solution<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>base_color<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">#635BFF<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>secondary_color<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">#ea5455<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>otp_exp_seconds<\\/span>\\\" => <span class=sf-dump-num>3600<\\/span>\\n    \\\"<span class=sf-dump-key>timezone<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">Asia\\/Dhaka<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>user_registration<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>secure_password<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>agree_policy<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>force_ssl<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>email_verification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>sms_verification<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>email_notification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>push_notification<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>kyc_verification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>site_logo_dark<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\">seeder\\/logo-white.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_logo<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"20 characters\\\">seeder\\/logo-dark.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_fav_dark<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"23 characters\\\">seeder\\/favicon-dark.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_fav<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/favicon-white.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>mail_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"105 characters\\\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>mail_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>push_notification_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"153 characters\\\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>push_notification_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>broadcast_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"128 characters\\\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>broadcast_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>sms_config<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>sms_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>web_version<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">3.6.0<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:31<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>site_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">CardApp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_title<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"29 characters\\\"> Virtual Credit Card Solution<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>base_color<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">#635BFF<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>secondary_color<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">#ea5455<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>otp_exp_seconds<\\/span>\\\" => <span class=sf-dump-num>3600<\\/span>\\n    \\\"<span class=sf-dump-key>timezone<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">Asia\\/Dhaka<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>user_registration<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>secure_password<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>agree_policy<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>force_ssl<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>email_verification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>sms_verification<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>email_notification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>push_notification<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>kyc_verification<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>site_logo_dark<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"21 characters\\\">seeder\\/logo-white.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_logo<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"20 characters\\\">seeder\\/logo-dark.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_fav_dark<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"23 characters\\\">seeder\\/favicon-dark.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>site_fav<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/favicon-white.png<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>mail_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"105 characters\\\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>mail_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>push_notification_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"153 characters\\\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>push_notification_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>broadcast_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"128 characters\\\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>broadcast_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>sms_config<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>sms_activity<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>web_version<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">3.6.0<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:3<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>mail_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">object<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>push_notification_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">object<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>broadcast_config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">object<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-843815548\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"card_details\": \"<pre class=sf-dump id=sf-dump-1016413126 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\VirtualCardApi<\\/span> {<a class=sf-dump-ref>#1628<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"17 characters\\\">virtual_card_apis<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"139 characters\\\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;\\/p&gt;<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"1301 characters\\\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\\\\\/\\\\\\/api.flutterwave.com\\\\\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\\\\\/\\\\\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\\\\\/\\\\\\/api.stripe.com\\\\\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\\\\\/\\\\\\/strowallet.com\\\\\\/api\\\\\\/bitvcard\\\\\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/virtual-card.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"139 characters\\\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;\\/p&gt;<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"1301 characters\\\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\\\\\/\\\\\\/api.flutterwave.com\\\\\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\\\\\/\\\\\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\\\\\/\\\\\\/api.stripe.com\\\\\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\\\\\/\\\\\\/strowallet.com\\\\\\/api\\\\\\/bitvcard\\\\\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/virtual-card.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:4<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">object<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1016413126\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"default_currency\": \"<pre class=sf-dump id=sf-dump-247597618 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\Admin\\\\Currency<\\/span> {<a class=sf-dump-ref>#1641<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"10 characters\\\">currencies<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:15<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">United States<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"20 characters\\\">United States dollar<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>code<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">USD<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>symbol<\\/span>\\\" => \\\"<span class=sf-dump-str>$<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>type<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">FIAT<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>flag<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"41 characters\\\">5d371250-25f9-449e-b17d-a46b71472681.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>rate<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">1.00000000<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>sender<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>receiver<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>default<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:15<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">United States<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"20 characters\\\">United States dollar<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>code<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"3 characters\\\">USD<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>symbol<\\/span>\\\" => \\\"<span class=sf-dump-str>$<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>type<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">FIAT<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>flag<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"41 characters\\\">5d371250-25f9-449e-b17d-a46b71472681.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>rate<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">1.00000000<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>sender<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>receiver<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>default<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:10<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>country<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>code<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>flag<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>rate<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">double<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>sender<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>receiver<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>default<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: <span class=sf-dump-note>array:4<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"4 characters\\\">both<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">senderCurrency<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">receiverCurrency<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">editData<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-247597618\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"__languages\": \"<pre class=sf-dump id=sf-dump-1926983293 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1481<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:2<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => <span class=sf-dump-note title=\\\"App\\\\Models\\\\Admin\\\\Language\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Admin<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span>Language<\\/span> {<a class=sf-dump-ref>#1629<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"9 characters\\\">languages<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:5<\\/span> [ &#8230;5]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n    <\\/samp>}\\n    <span class=sf-dump-index>1<\\/span> => <span class=sf-dump-note title=\\\"App\\\\Models\\\\Admin\\\\Language\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Admin<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span>Language<\\/span> {<a class=sf-dump-ref>#1634<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"9 characters\\\">languages<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:5<\\/span> [ &#8230;5]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1926983293\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"all_user_count\": \"<pre class=sf-dump id=sf-dump-747598123 data-indent-pad=\\\"  \\\"><span class=sf-dump-num>3<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-747598123\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"email_verified_user_count\": \"<pre class=sf-dump id=sf-dump-1420382738 data-indent-pad=\\\"  \\\"><span class=sf-dump-num>3<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-1420382738\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"kyc_verified_user_count\": \"<pre class=sf-dump id=sf-dump-781724349 data-indent-pad=\\\"  \\\"><span class=sf-dump-num>2<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-781724349\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"__extensions\": \"<pre class=sf-dump id=sf-dump-8879590 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1642<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => <span class=sf-dump-note title=\\\"App\\\\Models\\\\Admin\\\\Extension\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Admin<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span>Extension<\\/span> {<a class=sf-dump-ref>#1633<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"10 characters\\\">extensions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:11<\\/span> [ &#8230;11]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:11<\\/span> [ &#8230;11]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-8879590\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"pending_ticket_count\": \"<pre class=sf-dump id=sf-dump-219924585 data-indent-pad=\\\"  \\\"><span class=sf-dump-num>0<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-219924585\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"cardCharge\": \"<pre class=sf-dump id=sf-dump-1918435712 data-indent-pad=\\\"  \\\"><span class=sf-dump-const>null<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-1918435712\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"cardReloadCharge\": \"<pre class=sf-dump id=sf-dump-1623135005 data-indent-pad=\\\"  \\\"><span class=sf-dump-const>null<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-1623135005\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"card_limit\": \"<pre class=sf-dump id=sf-dump-1435698645 data-indent-pad=\\\"  \\\"><span class=sf-dump-num>3<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-1435698645\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"card_api\": \"<pre class=sf-dump id=sf-dump-1551618942 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\VirtualCardApi<\\/span> {<a class=sf-dump-ref>#1687<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"17 characters\\\">virtual_card_apis<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"139 characters\\\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;\\/p&gt;<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"1301 characters\\\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\\\\\/\\\\\\/api.flutterwave.com\\\\\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\\\\\/\\\\\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\\\\\/\\\\\\/api.stripe.com\\\\\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\\\\\/\\\\\\/strowallet.com\\\\\\/api\\\\\\/bitvcard\\\\\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/virtual-card.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:8<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => <span class=sf-dump-num>1<\\/span>\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"139 characters\\\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;\\/p&gt;<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"1301 characters\\\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\\\\\/\\\\\\/api.flutterwave.com\\\\\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\\\\\/\\\\\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\\\\\/\\\\\\/api.stripe.com\\\\\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\\\\\/\\\\\\/strowallet.com\\\\\\/api\\\\\\/bitvcard\\\\\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-22 14:47:13<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>image<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"24 characters\\\">seeder\\/virtual-card.webp<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => <span class=sf-dump-num>3<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:4<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>admin_id<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>config<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">object<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_details<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">string<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>card_limit<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">integer<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dates<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  <\\/samp>]\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1551618942\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"errors\": \"<pre class=sf-dump id=sf-dump-1054743465 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Support\\\\ViewErrorBag<\\/span> {<a class=sf-dump-ref>#1851<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">bags<\\/span>: []\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1054743465\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"page_title\": \"<pre class=sf-dump id=sf-dump-517142794 data-indent-pad=\\\"  \\\">\\\"<span class=sf-dump-str title=\\\"11 characters\\\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;<\\/span>\\\"\\n<\\/pre><script>Sfdump(\\\"sf-dump-517142794\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"networks\": \"<pre class=sf-dump id=sf-dump-1185497089 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1940<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1185497089\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"payment_gateways_currencies\": \"<pre class=sf-dump id=sf-dump-855030077 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1923<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-855030077\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"transactions\": \"<pre class=sf-dump id=sf-dump-595202320 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1944<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-595202320\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"PaymentGatewayCurrency\": \"<pre class=sf-dump id=sf-dump-1332462422 data-indent-pad=\\\"  \\\"><span class=sf-dump-const>null<\\/span>\\n<\\/pre><script>Sfdump(\\\"sf-dump-1332462422\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\"\n        }\n    },\n    \"userId\": 3,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.538906, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.184342, "end": **********.930465, "duration": 0.7461230754852295, "duration_str": "746ms", "measures": [{"label": "Booting", "start": **********.184342, "relative_start": 0, "end": **********.429274, "relative_end": **********.429274, "duration": 0.2449321746826172, "duration_str": "245ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.429284, "relative_start": 0.24494218826293945, "end": **********.930467, "relative_end": 1.9073486328125e-06, "duration": 0.5011827945709229, "duration_str": "501ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31252000, "peak_usage_str": "30MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Attempt to read property \"fixed_charge\" on null (View: D:\\projects\\CardApp\\resources\\views\\user\\sections\\add-money\\index.blade.php)", "code": 0, "file": "storage/framework/views/fd15137e9423402bba5323dfdedba21c0580ea69.php", "line": 65, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1869714236 data-indent-pad=\"  \"><span class=sf-dump-note>array:58</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">[object ErrorException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>70</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views/fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=sf-dump-note title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Factory</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21684 title=\"2 occurrences\">#1684</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref22 title=\"2 occurrences\">#2</a> &#8230;37}\n        \"<span class=sf-dump-key>basic_settings</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\BasicSettings\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BasicSettings</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21362 title=\"2 occurrences\">#1362</a><samp data-depth=5 id=sf-dump-1869714236-ref21362 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">basic_settings</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CardApp</span>\"\n            \"<span class=sf-dump-key>site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\"> Virtual Credit Card Solution</span>\"\n            \"<span class=sf-dump-key>base_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#635BFF</span>\"\n            \"<span class=sf-dump-key>secondary_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#ea5455</span>\"\n            \"<span class=sf-dump-key>otp_exp_seconds</span>\" => <span class=sf-dump-num>3600</span>\n            \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Asia/Dhaka</span>\"\n            \"<span class=sf-dump-key>user_registration</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>secure_password</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>agree_policy</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>force_ssl</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>sms_verification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_notification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>push_notification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>kyc_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_logo_dark</span>\" => \"<span class=sf-dump-str title=\"21 characters\">seeder/logo-white.png</span>\"\n            \"<span class=sf-dump-key>site_logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">seeder/logo-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav_dark</span>\" => \"<span class=sf-dump-str title=\"23 characters\">seeder/favicon-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/favicon-white.png</span>\"\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"105 characters\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>mail_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}</span>\"\n            \"<span class=sf-dump-key>push_notification_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"128 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}</span>\"\n            \"<span class=sf-dump-key>broadcast_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_config</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>web_version</span>\" => \"<span class=sf-dump-str title=\"5 characters\">3.6.0</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CardApp</span>\"\n            \"<span class=sf-dump-key>site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\"> Virtual Credit Card Solution</span>\"\n            \"<span class=sf-dump-key>base_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#635BFF</span>\"\n            \"<span class=sf-dump-key>secondary_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#ea5455</span>\"\n            \"<span class=sf-dump-key>otp_exp_seconds</span>\" => <span class=sf-dump-num>3600</span>\n            \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Asia/Dhaka</span>\"\n            \"<span class=sf-dump-key>user_registration</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>secure_password</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>agree_policy</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>force_ssl</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>sms_verification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_notification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>push_notification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>kyc_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_logo_dark</span>\" => \"<span class=sf-dump-str title=\"21 characters\">seeder/logo-white.png</span>\"\n            \"<span class=sf-dump-key>site_logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">seeder/logo-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav_dark</span>\" => \"<span class=sf-dump-str title=\"23 characters\">seeder/favicon-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/favicon-white.png</span>\"\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"105 characters\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>mail_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}</span>\"\n            \"<span class=sf-dump-key>push_notification_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"128 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}</span>\"\n            \"<span class=sf-dump-key>broadcast_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_config</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>web_version</span>\" => \"<span class=sf-dump-str title=\"5 characters\">3.6.0</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>card_details</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21628 title=\"2 occurrences\">#1628</a><samp data-depth=5 id=sf-dump-1869714236-ref21628 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"17 characters\">virtual_card_apis</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>default_currency</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\Currency\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Currency</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21641 title=\"2 occurrences\">#1641</a><samp data-depth=5 id=sf-dump-1869714236-ref21641 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">currencies</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">United States dollar</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FIAT</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"41 characters\">5d371250-25f9-449e-b17d-a46b71472681.webp</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1.00000000</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>receiver</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">United States dollar</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FIAT</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"41 characters\">5d371250-25f9-449e-b17d-a46b71472681.webp</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1.00000000</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>receiver</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"6 characters\">double</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>receiver</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>default</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">both</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">senderCurrency</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">receiverCurrency</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">editData</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>__languages</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21481 title=\"2 occurrences\">#1481</a><samp data-depth=5 id=sf-dump-1869714236-ref21481 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Language</span> {<a class=sf-dump-ref>#1629</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>last_edit_by</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Language</span> {<a class=sf-dump-ref>#1634</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Arabic</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">rtl</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Arabic</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">rtl</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>last_edit_by</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>all_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>email_verified_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>kyc_verified_user_count</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>__extensions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21642 title=\"2 occurrences\">#1642</a><samp data-depth=5 id=sf-dump-1869714236-ref21642 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Extension\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Extension</span> {<a class=sf-dump-ref>#1633</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">extensions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tawk</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tawk-to</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"302 characters\">Go to your tawk to dashbaord. Click [setting icon] on top bar. Then click [Chat Widget] link from sidebar and follow the screenshot bellow. Copy property ID and paste it in Property ID field. Then copy widget ID and paste it in Widget ID field. Finally click on [Update] button and you are ready to go.</span>\"\n                \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">logo-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>script</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"95 characters\">{&quot;property_id&quot;:{&quot;title&quot;:&quot;Property ID&quot;,&quot;value&quot;:&quot;&quot;},&quot;widget_id&quot;:{&quot;title&quot;:&quot;Widget ID&quot;,&quot;value&quot;:&quot;&quot;}}</span>\"\n                \"<span class=sf-dump-key>support_image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">instruction-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tawk</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tawk-to</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"302 characters\">Go to your tawk to dashbaord. Click [setting icon] on top bar. Then click [Chat Widget] link from sidebar and follow the screenshot bellow. Copy property ID and paste it in Property ID field. Then copy widget ID and paste it in Widget ID field. Finally click on [Update] button and you are ready to go.</span>\"\n                \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">logo-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>script</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"95 characters\">{&quot;property_id&quot;:{&quot;title&quot;:&quot;Property ID&quot;,&quot;value&quot;:&quot;&quot;},&quot;widget_id&quot;:{&quot;title&quot;:&quot;Widget ID&quot;,&quot;value&quot;:&quot;&quot;}}</span>\"\n                \"<span class=sf-dump-key>support_image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">instruction-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>pending_ticket_count</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>cardCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cardReloadCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>card_api</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21687 title=\"2 occurrences\">#1687</a><samp data-depth=5 id=sf-dump-1869714236-ref21687 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"17 characters\">virtual_card_apis</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21851 title=\"2 occurrences\">#1851</a><samp data-depth=5 id=sf-dump-1869714236-ref21851 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;</span>\"\n        \"<span class=sf-dump-key>networks</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21940 title=\"2 occurrences\">#1940</a><samp data-depth=5 id=sf-dump-1869714236-ref21940 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>payment_gateways_currencies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21923 title=\"2 occurrences\">#1923</a><samp data-depth=5 id=sf-dump-1869714236-ref21923 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21944 title=\"2 occurrences\">#1944</a><samp data-depth=5 id=sf-dump-1869714236-ref21944 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>PaymentGatewayCurrency</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>195</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"75 characters\">D:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=sf-dump-note title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Factory</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21684 title=\"2 occurrences\">#1684</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref22 title=\"2 occurrences\">#2</a> &#8230;37}\n        \"<span class=sf-dump-key>basic_settings</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\BasicSettings\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BasicSettings</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21362 title=\"2 occurrences\">#1362</a>}\n        \"<span class=sf-dump-key>card_details</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21628 title=\"2 occurrences\">#1628</a>}\n        \"<span class=sf-dump-key>default_currency</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\Currency\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Currency</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21641 title=\"2 occurrences\">#1641</a>}\n        \"<span class=sf-dump-key>__languages</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21481 title=\"2 occurrences\">#1481</a>}\n        \"<span class=sf-dump-key>all_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>email_verified_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>kyc_verified_user_count</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>__extensions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21642 title=\"2 occurrences\">#1642</a>}\n        \"<span class=sf-dump-key>pending_ticket_count</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>cardCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cardReloadCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>card_api</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21687 title=\"2 occurrences\">#1687</a>}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21851 title=\"2 occurrences\">#1851</a>}\n        \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;</span>\"\n        \"<span class=sf-dump-key>networks</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21940 title=\"2 occurrences\">#1940</a>}\n        \"<span class=sf-dump-key>payment_gateways_currencies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21923 title=\"2 occurrences\">#1923</a>}\n        \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-1869714236-ref21944 title=\"2 occurrences\">#1944</a>}\n        \"<span class=sf-dump-key>PaymentGatewayCurrency</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>178</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>147</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>906</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>875</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>797</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">app/Http/Middleware/VerificationGuard.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Http\\Middleware\\VerificationGuard</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Middleware/Admin/Localization.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Middleware\\Admin\\Localization</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>44</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>797</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>11</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">D:\\projects\\CardApp\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869714236\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                </div>\r\n", "                    </fieldset>\r\n", "                </div>\r\n", "                <p class='text--primary balance__text'><?php echo e(__('Charge')); ?>:  <span class='text--primary balance__text' dir=\"ltr\"><?php echo e($PaymentGatewayCurrency->fixed_charge); ?> USD</span> + <?php echo e($PaymentGatewayCurrency->percent_charge); ?>%</p>\r\n", "\r\n", "                <button id=\"confirm-btn\" type=\"button\" class=\"confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold\">\r\n", "                    <?php echo e(__('Confirm')); ?>\r\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fstorage%2Fframework%2Fviews%2Ffd15137e9423402bba5323dfdedba21c0580ea69.php&line=65", "ajax": false, "filename": "fd15137e9423402bba5323dfdedba21c0580ea69.php", "line": "65"}}, {"type": "ErrorException", "message": "Attempt to read property \"fixed_charge\" on null", "code": 0, "file": "storage/framework/views/fd15137e9423402bba5323dfdedba21c0580ea69.php", "line": 65, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-780957193 data-indent-pad=\"  \"><span class=sf-dump-note>array:62</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>272</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">handleError</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">Attempt to read property &quot;fixed_charge&quot; on null</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views\\fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>65</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"68 characters\">storage/framework/views/fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>65</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Bootstrap\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">Attempt to read property &quot;fixed_charge&quot; on null</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views\\fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>65</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views\\fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views/fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=sf-dump-note title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Factory</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21684 title=\"3 occurrences\">#1684</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref22 title=\"3 occurrences\">#2</a> &#8230;37}\n        \"<span class=sf-dump-key>basic_settings</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\BasicSettings\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BasicSettings</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21362 title=\"3 occurrences\">#1362</a><samp data-depth=5 id=sf-dump-780957193-ref21362 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">basic_settings</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CardApp</span>\"\n            \"<span class=sf-dump-key>site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\"> Virtual Credit Card Solution</span>\"\n            \"<span class=sf-dump-key>base_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#635BFF</span>\"\n            \"<span class=sf-dump-key>secondary_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#ea5455</span>\"\n            \"<span class=sf-dump-key>otp_exp_seconds</span>\" => <span class=sf-dump-num>3600</span>\n            \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Asia/Dhaka</span>\"\n            \"<span class=sf-dump-key>user_registration</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>secure_password</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>agree_policy</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>force_ssl</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>sms_verification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_notification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>push_notification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>kyc_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_logo_dark</span>\" => \"<span class=sf-dump-str title=\"21 characters\">seeder/logo-white.png</span>\"\n            \"<span class=sf-dump-key>site_logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">seeder/logo-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav_dark</span>\" => \"<span class=sf-dump-str title=\"23 characters\">seeder/favicon-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/favicon-white.png</span>\"\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"105 characters\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>mail_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}</span>\"\n            \"<span class=sf-dump-key>push_notification_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"128 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}</span>\"\n            \"<span class=sf-dump-key>broadcast_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_config</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>web_version</span>\" => \"<span class=sf-dump-str title=\"5 characters\">3.6.0</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CardApp</span>\"\n            \"<span class=sf-dump-key>site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\"> Virtual Credit Card Solution</span>\"\n            \"<span class=sf-dump-key>base_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#635BFF</span>\"\n            \"<span class=sf-dump-key>secondary_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#ea5455</span>\"\n            \"<span class=sf-dump-key>otp_exp_seconds</span>\" => <span class=sf-dump-num>3600</span>\n            \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Asia/Dhaka</span>\"\n            \"<span class=sf-dump-key>user_registration</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>secure_password</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>agree_policy</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>force_ssl</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>sms_verification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>email_notification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>push_notification</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>kyc_verification</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>site_logo_dark</span>\" => \"<span class=sf-dump-str title=\"21 characters\">seeder/logo-white.png</span>\"\n            \"<span class=sf-dump-key>site_logo</span>\" => \"<span class=sf-dump-str title=\"20 characters\">seeder/logo-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav_dark</span>\" => \"<span class=sf-dump-str title=\"23 characters\">seeder/favicon-dark.png</span>\"\n            \"<span class=sf-dump-key>site_fav</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/favicon-white.png</span>\"\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"105 characters\">{&quot;method&quot;:&quot;smtp&quot;,&quot;host&quot;:&quot;&quot;,&quot;port&quot;:&quot;&quot;,&quot;encryption&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;username&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;app_name&quot;:&quot;&quot;}</span>\"\n            \"<span class=sf-dump-key>mail_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;instance_id&quot;:&quot;255ae045-4995-4b74-9caf-b9b5101780df&quot;,&quot;primary_key&quot;:&quot;CDBB1D7FC33B562C63019647D3076998A14B97B251F651CB72B3934E49114200&quot;}</span>\"\n            \"<span class=sf-dump-key>push_notification_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"128 characters\">{&quot;method&quot;:&quot;pusher&quot;,&quot;app_id&quot;:&quot;1574360&quot;,&quot;primary_key&quot;:&quot;971ccaa6176db78407bf&quot;,&quot;secret_key&quot;:&quot; a30a6f1a61b97eb8225a&quot;,&quot;cluster&quot;:&quot;ap2&quot;}</span>\"\n            \"<span class=sf-dump-key>broadcast_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_config</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>sms_activity</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>web_version</span>\" => \"<span class=sf-dump-str title=\"5 characters\">3.6.0</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>mail_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>push_notification_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>broadcast_config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>card_details</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21628 title=\"3 occurrences\">#1628</a><samp data-depth=5 id=sf-dump-780957193-ref21628 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"17 characters\">virtual_card_apis</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>default_currency</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\Currency\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Currency</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21641 title=\"3 occurrences\">#1641</a><samp data-depth=5 id=sf-dump-780957193-ref21641 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">currencies</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">United States dollar</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FIAT</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"41 characters\">5d371250-25f9-449e-b17d-a46b71472681.webp</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1.00000000</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>receiver</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">United States dollar</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n            \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n            \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FIAT</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"41 characters\">5d371250-25f9-449e-b17d-a46b71472681.webp</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1.00000000</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>receiver</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>flag</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>rate</span>\" => \"<span class=sf-dump-str title=\"6 characters\">double</span>\"\n            \"<span class=sf-dump-key>sender</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>receiver</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>default</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">both</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">senderCurrency</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">receiverCurrency</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">editData</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>__languages</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21481 title=\"3 occurrences\">#1481</a><samp data-depth=5 id=sf-dump-780957193-ref21481 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Language</span> {<a class=sf-dump-ref>#1629</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">English</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>last_edit_by</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Language\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Language</span> {<a class=sf-dump-ref>#1634</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">languages</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Arabic</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">rtl</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Arabic</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>last_edit_by</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">rtl</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>last_edit_by</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>all_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>email_verified_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>kyc_verified_user_count</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>__extensions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21642 title=\"3 occurrences\">#1642</a><samp data-depth=5 id=sf-dump-780957193-ref21642 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Admin\\Extension\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Extension</span> {<a class=sf-dump-ref>#1633</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">extensions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tawk</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tawk-to</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"302 characters\">Go to your tawk to dashbaord. Click [setting icon] on top bar. Then click [Chat Widget] link from sidebar and follow the screenshot bellow. Copy property ID and paste it in Property ID field. Then copy widget ID and paste it in Widget ID field. Finally click on [Update] button and you are ready to go.</span>\"\n                \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">logo-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>script</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"95 characters\">{&quot;property_id&quot;:{&quot;title&quot;:&quot;Property ID&quot;,&quot;value&quot;:&quot;&quot;},&quot;widget_id&quot;:{&quot;title&quot;:&quot;Widget ID&quot;,&quot;value&quot;:&quot;&quot;}}</span>\"\n                \"<span class=sf-dump-key>support_image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">instruction-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tawk</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tawk-to</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"302 characters\">Go to your tawk to dashbaord. Click [setting icon] on top bar. Then click [Chat Widget] link from sidebar and follow the screenshot bellow. Copy property ID and paste it in Property ID field. Then copy widget ID and paste it in Widget ID field. Finally click on [Update] button and you are ready to go.</span>\"\n                \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">logo-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>script</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"95 characters\">{&quot;property_id&quot;:{&quot;title&quot;:&quot;Property ID&quot;,&quot;value&quot;:&quot;&quot;},&quot;widget_id&quot;:{&quot;title&quot;:&quot;Widget ID&quot;,&quot;value&quot;:&quot;&quot;}}</span>\"\n                \"<span class=sf-dump-key>support_image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">instruction-tawk-to.png</span>\"\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>shortcode</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>pending_ticket_count</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>cardCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cardReloadCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>card_api</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21687 title=\"3 occurrences\">#1687</a><samp data-depth=5 id=sf-dump-780957193-ref21687 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"17 characters\">virtual_card_apis</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>admin_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"139 characters\">&lt;p&gt;This card is property of CardApp, Wonderland. Misuse is criminal offense. If found, please return to CardApp or to the nearest bank.&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"1301 characters\">{&quot;flutterwave_secret_key&quot;:&quot;FLWSECK_TEST-SANDBOXDEMOKEY-X&quot;,&quot;flutterwave_secret_hash&quot;:&quot;AYxcfvgbhnj@34&quot;,&quot;flutterwave_url&quot;:&quot;https:\\/\\/api.flutterwave.com\\/v3&quot;,&quot;sudo_api_key&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MTKO352CEfxG4SUhpfAWu3mkHilLL8Y-oufD6WWCiH4&quot;,&quot;sudo_vault_id&quot;:&quot;tntbuyt0v9u&quot;,&quot;sudo_url&quot;:&quot;https:\\/\\/api.sandbox.sudo.cards&quot;,&quot;sudo_mode&quot;:&quot;sandbox&quot;,&quot;stripe_public_key&quot;:&quot;pk_test_51NjGM4K6kUt0AggqD10PfWJcB8NxJmDhDptSqXPpX2d4Xcj7KtXxIrw1zRgK4jI5SIm9ZB7JIhmeYjcTkF7eL8pc00TgiPUGg5&quot;,&quot;stripe_secret_key&quot;:&quot;sk_test_51NjGM4K6kUt0Aggqfejd1Xiixa6HEjQXJNljEwt9QQPOTWoyylaIAhccSBGxWBnvDGw0fptTvGWXJ5kBO7tdpLNG00v5cWHt96&quot;,&quot;stripe_url&quot;:&quot;https:\\/\\/api.stripe.com\\/v1&quot;,&quot;strowallet_public_key&quot;:&quot;R67MNEPQV2ABQW9HDD7JQFXQ2AJMMY&quot;,&quot;strowallet_secret_key&quot;:&quot;AOC963E385FORPRRCXQJ698C1Q953B&quot;,&quot;strowallet_url&quot;:&quot;https:\\/\\/strowallet.com\\/api\\/bitvcard\\/&quot;,&quot;strowallet_mode&quot;:&quot;sandbox&quot;,&quot;name&quot;:&quot;strowallet&quot;}</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-22 14:47:13</span>\"\n            \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"24 characters\">seeder/virtual-card.webp</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>admin_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"6 characters\">object</span>\"\n            \"<span class=sf-dump-key>card_details</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            \"<span class=sf-dump-key>card_limit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21851 title=\"3 occurrences\">#1851</a><samp data-depth=5 id=sf-dump-780957193-ref21851 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;</span>\"\n        \"<span class=sf-dump-key>networks</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21940 title=\"3 occurrences\">#1940</a><samp data-depth=5 id=sf-dump-780957193-ref21940 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>payment_gateways_currencies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21923 title=\"3 occurrences\">#1923</a><samp data-depth=5 id=sf-dump-780957193-ref21923 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21944 title=\"3 occurrences\">#1944</a><samp data-depth=5 id=sf-dump-780957193-ref21944 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>PaymentGatewayCurrency</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>70</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">D:\\projects\\CardApp\\storage\\framework\\views/fd15137e9423402bba5323dfdedba21c0580ea69.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=sf-dump-note title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Factory</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21684 title=\"3 occurrences\">#1684</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref22 title=\"3 occurrences\">#2</a> &#8230;37}\n        \"<span class=sf-dump-key>basic_settings</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\BasicSettings\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BasicSettings</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21362 title=\"3 occurrences\">#1362</a>}\n        \"<span class=sf-dump-key>card_details</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21628 title=\"3 occurrences\">#1628</a>}\n        \"<span class=sf-dump-key>default_currency</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\Currency\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Currency</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21641 title=\"3 occurrences\">#1641</a>}\n        \"<span class=sf-dump-key>__languages</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21481 title=\"3 occurrences\">#1481</a>}\n        \"<span class=sf-dump-key>all_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>email_verified_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>kyc_verified_user_count</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>__extensions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21642 title=\"3 occurrences\">#1642</a>}\n        \"<span class=sf-dump-key>pending_ticket_count</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>cardCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cardReloadCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>card_api</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21687 title=\"3 occurrences\">#1687</a>}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21851 title=\"3 occurrences\">#1851</a>}\n        \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;</span>\"\n        \"<span class=sf-dump-key>networks</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21940 title=\"3 occurrences\">#1940</a>}\n        \"<span class=sf-dump-key>payment_gateways_currencies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21923 title=\"3 occurrences\">#1923</a>}\n        \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21944 title=\"3 occurrences\">#1944</a>}\n        \"<span class=sf-dump-key>PaymentGatewayCurrency</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>195</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"75 characters\">D:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=sf-dump-note title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Factory</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21684 title=\"3 occurrences\">#1684</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref22 title=\"3 occurrences\">#2</a> &#8230;37}\n        \"<span class=sf-dump-key>basic_settings</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\BasicSettings\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>BasicSettings</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21362 title=\"3 occurrences\">#1362</a>}\n        \"<span class=sf-dump-key>card_details</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21628 title=\"3 occurrences\">#1628</a>}\n        \"<span class=sf-dump-key>default_currency</span>\" => <span class=sf-dump-note title=\"App\\Models\\Admin\\Currency\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Admin</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Currency</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21641 title=\"3 occurrences\">#1641</a>}\n        \"<span class=sf-dump-key>__languages</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21481 title=\"3 occurrences\">#1481</a>}\n        \"<span class=sf-dump-key>all_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>email_verified_user_count</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>kyc_verified_user_count</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>__extensions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21642 title=\"3 occurrences\">#1642</a>}\n        \"<span class=sf-dump-key>pending_ticket_count</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>cardCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>cardReloadCharge</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>card_limit</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>card_api</span>\" => <span class=sf-dump-note title=\"App\\Models\\VirtualCardApi\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>VirtualCardApi</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21687 title=\"3 occurrences\">#1687</a>}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ViewErrorBag</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21851 title=\"3 occurrences\">#1851</a>}\n        \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1573;&#1610;&#1583;&#1575;&#1593; &#1571;&#1605;&#1608;&#1575;&#1604;</span>\"\n        \"<span class=sf-dump-key>networks</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21940 title=\"3 occurrences\">#1940</a>}\n        \"<span class=sf-dump-key>payment_gateways_currencies</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21923 title=\"3 occurrences\">#1923</a>}\n        \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-780957193-ref21944 title=\"3 occurrences\">#1944</a>}\n        \"<span class=sf-dump-key>PaymentGatewayCurrency</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>178</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>147</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>906</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>875</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>797</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">app/Http/Middleware/VerificationGuard.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">App\\Http\\Middleware\\VerificationGuard</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Middleware/Admin/Localization.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Middleware\\Admin\\Localization</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>44</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>797</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>776</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>11</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">D:\\projects\\CardApp\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-780957193\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                </div>\r\n", "                    </fieldset>\r\n", "                </div>\r\n", "                <p class='text--primary balance__text'><?php echo e(__('Charge')); ?>:  <span class='text--primary balance__text' dir=\"ltr\"><?php echo e($PaymentGatewayCurrency->fixed_charge); ?> USD</span> + <?php echo e($PaymentGatewayCurrency->percent_charge); ?>%</p>\r\n", "\r\n", "                <button id=\"confirm-btn\" type=\"button\" class=\"confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold\">\r\n", "                    <?php echo e(__('Confirm')); ?>\r\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fstorage%2Fframework%2Fviews%2Ffd15137e9423402bba5323dfdedba21c0580ea69.php&line=65", "ajax": false, "filename": "fd15137e9423402bba5323dfdedba21c0580ea69.php", "line": "65"}}]}, "views": {"nb_templates": 18, "templates": [{"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.495572, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.503841, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.877081, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.8777, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.878164, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.878676, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.879177, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.879722, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.880188, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.880688, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.898836, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.899481, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.900041, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.900638, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.901131, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.901654, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.sections.add-money.index", "param_count": null, "params": [], "start": **********.902121, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/add-money/index.blade.phpuser.sections.add-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Fadd-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.902671, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}]}, "route": {"uri": "GET user/add-money", "middleware": "web, auth, verification.guard", "controller": "App\\Http\\Controllers\\User\\AddMoneyController@index", "as": "user.add.money.index", "namespace": null, "prefix": "user/add-money", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=41\" onclick=\"\">app/Http/Controllers/User/AddMoneyController.php:41-53</a>"}, "queries": {"nb_statements": 18, "nb_visible_statements": 18, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0179, "accumulated_duration_str": "17.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.459097, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 4.022}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.4622018, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 4.022, "width_percent": 2.291}, {"sql": "select * from `user_wallets` where `user_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4678411, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:43", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=43", "ajax": false, "filename": "AddMoneyController.php", "line": "43"}, "connection": "cardapp", "explain": null, "start_percent": 6.313, "width_percent": 3.687}, {"sql": "select * from `currencies` where `id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 44}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.469625, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:44", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=44", "ajax": false, "filename": "AddMoneyController.php", "line": "44"}, "connection": "cardapp", "explain": null, "start_percent": 10, "width_percent": 2.57}, {"sql": "select * from `networks`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 45}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4713218, "duration": 0.00497, "duration_str": "4.97ms", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:45", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=45", "ajax": false, "filename": "AddMoneyController.php", "line": "45"}, "connection": "cardapp", "explain": null, "start_percent": 12.57, "width_percent": 27.765}, {"sql": "select * from `payment_gateway_currencies` where exists (select * from `payment_gateways` where `payment_gateway_currencies`.`payment_gateway_id` = `payment_gateways`.`id` and `slug` = 'add-money' and `status` = 1)", "type": "query", "params": [], "bindings": ["add-money", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 49}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.481147, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:49", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=49", "ajax": false, "filename": "AddMoneyController.php", "line": "49"}, "connection": "cardapp", "explain": null, "start_percent": 40.335, "width_percent": 10.503}, {"sql": "select * from `transactions` where `user_id` = 3 and `type` = 'ADD-MONEY' order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [3, "ADD-MONEY"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 50}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.485101, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:50", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=50", "ajax": false, "filename": "AddMoneyController.php", "line": "50"}, "connection": "cardapp", "explain": null, "start_percent": 50.838, "width_percent": 3.911}, {"sql": "select * from `payment_gateway_currencies` where `alias` = 'add-money-usdt-usd-automatic' limit 1", "type": "query", "params": [], "bindings": ["add-money-usdt-usd-automatic"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.486749, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:51", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/User/AddMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\AddMoneyController.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAddMoneyController.php&line=51", "ajax": false, "filename": "AddMoneyController.php", "line": "51"}, "connection": "cardapp", "explain": null, "start_percent": 54.749, "width_percent": 2.737}, {"sql": "select count(*) as aggregate from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 26, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/RemoveRequestIp.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\RemoveRequestIp.php", "line": 11}], "start": **********.577475, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "User.php:208", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=208", "ajax": false, "filename": "User.php", "line": "208"}, "connection": "cardapp", "explain": null, "start_percent": 57.486, "width_percent": 3.855}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 23, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/RemoveRequestIp.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\RemoveRequestIp.php", "line": 11}], "start": **********.579866, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "User.php:209", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=209", "ajax": false, "filename": "User.php", "line": "209"}, "connection": "cardapp", "explain": null, "start_percent": 61.341, "width_percent": 3.017}, {"sql": "select count(*) as aggregate from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 26, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/CensorRequestBodyFields.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\CensorRequestBodyFields.php", "line": 18}], "start": **********.825021, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "User.php:208", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=208", "ajax": false, "filename": "User.php", "line": "208"}, "connection": "cardapp", "explain": null, "start_percent": 64.358, "width_percent": 6.592}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 23, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/CensorRequestBodyFields.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\CensorRequestBodyFields.php", "line": 18}], "start": **********.829561, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:209", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=209", "ajax": false, "filename": "User.php", "line": "209"}, "connection": "cardapp", "explain": null, "start_percent": 70.95, "width_percent": 5.922}, {"sql": "select count(*) as aggregate from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 26, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/CensorRequestHeaders.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\CensorRequestHeaders.php", "line": 18}], "start": **********.835339, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "User.php:208", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=208", "ajax": false, "filename": "User.php", "line": "208"}, "connection": "cardapp", "explain": null, "start_percent": 76.872, "width_percent": 5.922}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 23, "namespace": null, "name": "vendor/spatie/flare-client-php/src/FlareMiddleware/CensorRequestHeaders.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\FlareMiddleware\\CensorRequestHeaders.php", "line": 18}], "start": **********.838747, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "User.php:209", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=209", "ajax": false, "filename": "User.php", "line": "209"}, "connection": "cardapp", "explain": null, "start_percent": 82.793, "width_percent": 5.698}, {"sql": "select count(*) as aggregate from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 26, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 404}], "start": **********.895595, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "User.php:208", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=208", "ajax": false, "filename": "User.php", "line": "208"}, "connection": "cardapp", "explain": null, "start_percent": 88.492, "width_percent": 3.128}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 23, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 404}], "start": **********.89705, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "User.php:209", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=209", "ajax": false, "filename": "User.php", "line": "209"}, "connection": "cardapp", "explain": null, "start_percent": 91.62, "width_percent": 2.346}, {"sql": "select count(*) as aggregate from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 25, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 26, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 404}], "start": **********.916342, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "User.php:208", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 208}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=208", "ajax": false, "filename": "User.php", "line": "208"}, "connection": "cardapp", "explain": null, "start_percent": 93.966, "width_percent": 3.073}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` = 3 and `user_login_logs`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-ignition/src/ContextProviders/LaravelRequestContextProvider.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\laravel-ignition\\src\\ContextProviders\\LaravelRequestContextProvider.php", "line": 96}, {"index": 22, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 314}, {"index": 23, "namespace": null, "name": "vendor/spatie/flare-client-php/src/Report.php", "file": "D:\\projects\\CardApp\\vendor\\spatie\\flare-client-php\\src\\Report.php", "line": 404}], "start": **********.917841, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "User.php:209", "source": {"index": 15, "namespace": null, "name": "app/Models/User.php", "file": "D:\\projects\\CardApp\\app\\Models\\User.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=209", "ajax": false, "filename": "User.php", "line": "209"}, "connection": "cardapp", "explain": null, "start_percent": 97.039, "width_percent": 2.961}]}, "models": {"data": {"App\\Models\\UserLoginLog": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUserLoginLog.php&line=1", "ajax": false, "filename": "UserLoginLog.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUserWallet.php&line=1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Admin\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/user/add-money\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750583843\n]"}, "request": {"path_info": "/user/add-money", "status_code": "<pre class=sf-dump id=sf-dump-1463974032 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1463974032\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1269803354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1269803354\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-195360346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-195360346\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-647990390 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjNDWjVZQ1B2eVluSWRUR2ZpOTUxUVE9PSIsInZhbHVlIjoiR2RwK0xaVzdtbUZINGtvakJzT1lyKy9MdFZibkFJVTFXTjZZYk1zSnZ3L0FWa1lCamk5WUJiU3NRMURWSnozVERZcVVIdXY1Z1F4Y25wZzF5R2d5aStRUUJZQXRIb3pGaXg3eTNmRzBiWFFuN1ZkZXAvVzFKY0VwcUt3TVhYL1QiLCJtYWMiOiIzNGIyMDUwMDM3MjRhZjRiYTc3ODYwMTgxMDhlMzEyMmI5MTQyYWI3MWFkMzRmZmQwYmY5MTVhYTRlZTgxYzhiIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IlJ0Y0w2aXd2TjgzOGVGRkNidnk5SlE9PSIsInZhbHVlIjoiYnlqY2lUanpMUXNMamhVbHVrb3p6aVJOWiswTHY2SE9CV2d6SGZnRUFIbWZXYXhTS3dWMTRVcEdHVC84dUlCdDJsM0xDWDExNmVtUXJwQWJKM1RWemtsYlB4blM3RzU4dHpUVDF0T3NYUFhWZFpSVHUzZFFEYzJmK3lLOVRwTVgiLCJtYWMiOiJiMTk0ZmEwMGIwMGY1MTU3N2I1MzEwMzVjMTFlYTU3ZDA2NWFmZTQ1MWYzYzZhMDMyZmNhYzJlNDk5NDg3ZDUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647990390\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1854223331 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SJcBaZ7iTTWzVst4B6KdlZjJwU9k5yXwCvFs50gr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854223331\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1975414721 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:17:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlQ5bnFNUzJ2WEVsci9FR24vZnQ1QXc9PSIsInZhbHVlIjoiSmF4Ymc5dUpoQllrNGNlZFhuSDRNL3l3dTJYbWR0R0toVEJHQjlEMyt6MkdDRUNNUFVsYU9qeTVEb1lxSllEQ3A3NXRmL3pMRnRCK0U4OFM1WDgzRHJYVEpkUGhxSHhJREx1NGRpMjFIRjRRMSs5NlVoTktMdlF3RmN2cjJDaSsiLCJtYWMiOiJlM2RjNzdkZDJjYmM3NWRiMWUyZmRjNWFkZDEyZjU5OWFiOGEwODUwN2FjMWQyMjg1MjAwMGQ1NzZmNmU5ZWVjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IlJ2aGtDNlhQUTFhK08zQllOWmJ4SUE9PSIsInZhbHVlIjoiMlo5SlJ1OWR2Z1RQd3hMUXN6emUybU96cmtrSmtqbnFZeGFIeGRmT2pZS1d5WGNlSlZ5d3ZkaklQeHFpbVpObmM2VGNZdnAxT2MzeTk1cllzZlo4cFUyUkdVZDIvNkZUQmFRb2sxTjFMZU11K0h4UW4zbWxWbjRGOTlLN0Q2aHciLCJtYWMiOiI3MGViNjA3OTBlZWM0Mjc3Yjc2NTRiNDQxYmY2ZmZlMzdiY2Y1ZTc2Zjc4NzY2NGNkMTdjOTg5OTY4ZGM1NWY5IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlQ5bnFNUzJ2WEVsci9FR24vZnQ1QXc9PSIsInZhbHVlIjoiSmF4Ymc5dUpoQllrNGNlZFhuSDRNL3l3dTJYbWR0R0toVEJHQjlEMyt6MkdDRUNNUFVsYU9qeTVEb1lxSllEQ3A3NXRmL3pMRnRCK0U4OFM1WDgzRHJYVEpkUGhxSHhJREx1NGRpMjFIRjRRMSs5NlVoTktMdlF3RmN2cjJDaSsiLCJtYWMiOiJlM2RjNzdkZDJjYmM3NWRiMWUyZmRjNWFkZDEyZjU5OWFiOGEwODUwN2FjMWQyMjg1MjAwMGQ1NzZmNmU5ZWVjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IlJ2aGtDNlhQUTFhK08zQllOWmJ4SUE9PSIsInZhbHVlIjoiMlo5SlJ1OWR2Z1RQd3hMUXN6emUybU96cmtrSmtqbnFZeGFIeGRmT2pZS1d5WGNlSlZ5d3ZkaklQeHFpbVpObmM2VGNZdnAxT2MzeTk1cllzZlo4cFUyUkdVZDIvNkZUQmFRb2sxTjFMZU11K0h4UW4zbWxWbjRGOTlLN0Q2aHciLCJtYWMiOiI3MGViNjA3OTBlZWM0Mjc3Yjc2NTRiNDQxYmY2ZmZlMzdiY2Y1ZTc2Zjc4NzY2NGNkMTdjOTg5OTY4ZGM1NWY5IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975414721\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1193395775 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/add-money</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750583843</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193395775\", {\"maxDepth\":0})</script>\n"}}