@php
          $transactions = App\Models\Transaction::auth()->latest()->take(5)->get();
@endphp

<style>
/* language section start */
.language-dropdown {
    position: relative;
    display: inline-block;
    margin: 5px 0 0 0;
}

.dropdown-btn {
    cursor: pointer;
    background: transparent;
}

.dropdown-menu {
    display: none;
    position: absolute;
    background-color: white;
    width: 60px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    list-style: none;
    padding: 0;
    inset-inline-end: -48px;
    top:40px;
  }


.dropdown-menu li {
    cursor: pointer;
}

.language-dropdown .selected {
    font-weight: bold;
}
a{
  text-decoration:none;
  color:black;
}
nav ul li {
  position: relative;
  margin: 0 10px;
}
nav ul li.nav-item::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background: #3731ac;
  content: "";
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}
nav ul li:hover::after {
  width: 100%;
  visibility: visible;
  opacity: 1;
  transition: all 0.3s;
}
.notification__hover{
  transition: all .3s ease-in-out;

}

.notification__hover:hover{
  background: #ddd;
  padding:0px 8px;
  border-radius:8px;
}


/* language section end */
</style>

<nav class="navbar-wrapper">
    <div class="dashboard-title-part">
        <div class="left d-lg-block d-none">
          @yield('breadcrumb')
        </div>
      <div class='d-flex align-items-center bg-transparent gap-3 d-md-none'>
        <button class='burger bg-transparent' type='button'><img src="{{ asset('frontend/assets/icons/burger.svg') }}" alt="burger" style='object-fit:cover;'></button>
        <h2 style='font-size:18px; margin:0' class="fw-bold d-md-none">{{ __('welcome_back') }}, <span>{{ auth()->user()->fullname }}</span> 
        <img src="{{ asset('frontend/assets/images/hand.svg') }}" alt="{{ __('waving_hand') }}">
        </div>
        </h2>
        <div class="right">
        <!-- language start -->
        <div class="language-dropdown">
    <button class="dropdown-btn">
    <img width='20' src="{{ asset('frontend/assets/icons/language-switcher.svg') }}" alt="language switcher">
    </button>
    <ul class="dropdown-menu p-2">
      <li class='nav-item'>
        <a href='{{ route('lang', ['lang' => 'en' ]) }}'>English</a>
      </li>
      <li class='nav-item'>
        <a href='{{ route('lang', ['lang' => 'ar' ]) }}'>Arabic</a>
      </li>
    </ul>
</div>
<!-- language end -->



        <div class="dropdown" aria-labelledby="dropdownMenuButton">
          <button
            id="dropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            class="bg-transparent"
            style="margin: 0 auto 0 0"
          >
            <img
              id="notificationIcon"
              src="{{ asset('frontend/assets/icons/bill.svg') }}"
              alt="notification"
            />
          </button>
          <ul class="dropdown-menu p-3 note-ul" style="border-radius: 16px;">

            <!-- one notification start -->
            @foreach ( $transactions as $index => $item )
            @php
            // Assuming $item->status contains the status value
            switch ($item->status) {
                case 1:
                    $statusText = __('successful');
                    $statusClass = '--success';
                    break;
                case 2:
                    $statusText = __('pending');
                    $statusClass = '--pending';
                    break;
                case 3:
                    $statusText = __('on_hold');
                    $statusClass = '--hold';
                    break;
                case 4:
                    $statusText = __('rejected');
                    $statusClass = '--rejected';
                    break;
                default:
                    $statusText = __('failed');
                    $statusClass = '--failed';
                    break;
            }
        
            // Extract the part of the remark before "by"
            $remark = $item->remark;
            $position = strpos($remark, 'by');
        
            if ($position !== false) {
                $remarkBeforeBy = substr($remark, 0, $position); // Extract the part before "by"
            } else {
                $remarkBeforeBy = $remark; // Use the full remark if "by" is not found
            }
        @endphp


            <li class="d-flex justify-content-between my-2 notification__hover">
              <div class="d-flex align-items-center gap-2">
                <img src="{{ asset('frontend/assets/icons/Bell2.png') }}" />
                <div
                  class="d-flex flex-column">

                  {{-- <span class="text-dark fw-medium"> {{ substr($remark, $position + 3) }} </span> --}}
                  @if ($position)
                  <span class="text--primary">{{ __($remarkBeforeBy) }}</span>
                  @else
                      <span class="text--primary">{{ __($item->remark) }}</span>
                  @endif
                  
                  <div class="text-dark fw-medium d-flex align-items-center gap-1">
                    <span
                      id="notif__status"
                      class="{{ $statusClass }}"
                      aria-placeholder="active,closed"
                    ></span>
                    <span>{{ $statusText }}</span>
                  </div>
                </div>
              </div>
              <div>
                <p class="transaction__card__date text-dark fw-medium mb-0">
                  {{ $item->created_at }}
                </p>
                <p class="primary_color fw-bold">${{ $item->payable }}</p>
              </div>
            </li>
              @if ($index + 1 < count($transactions))
              <span></span>
              @endif

            @endforeach
            
      
          </ul>
          </div>
        </div>
    </div>
</nav>


<script>
  
  //  ! start

 var dropdownBtn = document.querySelector(".language-dropdown .dropdown-btn");
    var dropdownMenu = document.querySelector(".language-dropdown .dropdown-menu");
    var dropdownItems = document.querySelectorAll(".language-dropdown .dropdown-menu li");

    // Toggle the dropdown menu visibility
    dropdownBtn.addEventListener("click", function() {
        dropdownMenu.classList.toggle("show");
    });

    // Handle dropdown item click
    dropdownItems.forEach(function(item) {
        item.addEventListener("click", function() {
            var selectedLanguage = this.textContent;
            var selectedValue = this.getAttribute("data-value");
            
            dropdownBtn.textContent = selectedLanguage;
            dropdownItems.forEach(function(li) {
                li.classList.remove("selected");
            });
            this.classList.add("selected");

            // Perform the action when a language is selected
            console.log("Selected Language Code:", selectedValue);

            dropdownMenu.classList.remove("show"); // Hide the dropdown after selection
        });
    });

    // Close the dropdown if clicked outside
    document.addEventListener("click", function(e) {
        if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
            dropdownMenu.classList.remove("show");
        }
    });
    //  ! en

</script>


