@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/virtual-card.css') }}" />
<link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')

<section class="container-fluid justify-content-between border--primary p-4 rounded-3">
  <div class="row justify-content-between"> 
    <!-- details section -->
    <div class="col-xl-6 card__details_des p-3" style="background: #f7f7f7;color: #555555; font-size: 12px; display: flex; flex-direction: column; gap: 5px;">
      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/balance.svg') }}" alt="balance" />
          <span class='balance__text'>{{ __('Card Balance') }}</span>
        </div>
        <div class="col text-end d-flex justify-content-end d-flex justify-content-end">
          <button class="bg-transparent eye-toggle">
            <img src="{{ asset('frontend/assets/icons/eye.svg') }}" alt="{{ __('show') }}" />
          </button>
          <span data-full="{{ $result['balance'] }}">**** USD</span>
        </div>
      </div>
      
      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/user.svg') }}" alt="{{ __('Card Owner') }}" />
          <span class='balance__text'>{{ __('Card Holder Name') }}</span>
        </div>
        <div class="col text-end d-flex justify-content-end">
          <span class='balance__text'>{{ $result['holder_name'] }}</span>
        </div>
      </div>

      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/currency.svg') }}" alt="{{ __('Currency') }}" />
          <span class='balance__text'>{{ __('Currency') }}</span>
        </div>
        <div class="col text-end d-flex justify-content-end">
          <span class='balance__text'>{{ $result['currency'] }}</span>
        </div>
      </div>

      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/card_type.svg') }}" alt="{{ __('Card Number') }}" />
          <span class='balance__text'>{{ __('Card Number') }}</span>
        </div>
        <div class="col text-end d-flex justify-content-end">
          <button class="bg-transparent" onclick='copyHandler("{{ $result["cardNumber"] }}", this)'>
            <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('copy') }}" />
          </button>
          <button class="bg-transparent eye-toggle">
            <img src="{{ asset('frontend/assets/icons/eye.svg') }}" alt="{{ __('show') }}" />
          </button>
          <span dir="ltr" data-full="{{ $result['cardNumber'] }}">
            {{ substr($result['maskedNumber'], 0, 6) . "******" . substr($result['maskedNumber'], -4) }}
          </span>
        </div>
      </div>

      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/ccv.svg') }}" alt="{{ __('CCV') }}" />
          <span class='balance__text'>CCV</span>
        </div>
        <div class="col text-end d-flex justify-content-end">
        <button class="bg-transparent" onclick='copyHandler("{{ $result["cvv"] }}", this)'>
            <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('copy') }}" />
          </button>
          <button class="bg-transparent eye-toggle">
            <img src="{{ asset('frontend/assets/icons/eye.svg') }}" alt="{{ __('show') }}" />
          </button>
          <span data-full="{{ $result['cvv'] }}">***</span>
        </div>
      </div>
      
      <!-- one detail -->
      <div class="row">
        <div class="col">
          <img src="{{ asset('frontend/assets/icons/card_date.svg') }}" alt="{{ __('Expiration Date') }}" />
          <span class='balance__text'>{{ __('Expiration Date') }}</span>
        </div>
        <div class="col text-end d-flex justify-content-end">
          <span class='balance__text'>{{ $result['expiration'] }}</span> 
        </div>
      </div>
        
    </div>
    <!-- details section -->

    <!-- card section start-->
    <div class="col d-flex flex-column justify-content-center align-items-center" style="margin-top: 10px;">
      <div class="d-flex gap-3"> 
        <button class="virtual_card_actions__rotation_button" style="all: unset; cursor: pointer">
          <img src="{{ asset('frontend/assets/images/rotation_icon.svg') }}" />
        </button>
        <div class="virtual_card_actions__card">
          <div class="flip-card">
            <div class="flip-card-inner">
              <div class="flip-card-front">
                <div class="card-front-inner">
                  <div class="d-flex justify-content-end">
                    @php
                    $cardTypeImage = $result['cardType'] == 'Visa' ? 'visa.svg' : 'mastercard.svg';
                  @endphp
                  <img src="{{ asset('frontend/assets/icons/' . $cardTypeImage) }}" alt="{{ $result['cardType'] }}" />
                  </div>
                  <h6 class="d-flex justify-content-end card_number text-white" dir="ltr">
                    {{ substr($result['maskedNumber'], 0, 6) . "******" . substr($result['maskedNumber'], -4) }}
                  </h6>
                  <span class="d-flex align-items-baseline justify-content-between text-white">
                    <p class="card_exp text-dark">{{ __('Exp') }} {{ $result['expiration'] }}</p>
                    <h5>{{ $result['holder_name'] }}</h5>
                  </span>
                </div>
              </div>
              <div class="flip-card-back">
                <p class='ccv_number'>{{ $result['cvv'] }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- actions start -->
      <div class="justify-content-center p-3 rounded">
        <div class="d-flex align-items-center">
          <!-- charge start -->
          <div class="text-center mb-2">
            <button data-bs-toggle="modal" data-bs-target="#cardRecharge" class="bg-transparent d-flex gap-2 align-items-center justify-content-center w-100">
              <img src="{{ asset('frontend/assets/images/charge_icon.svg') }}" alt="{{ __('Charge Card') }}" class="me-2" />
              <span>{{ __('Charge Card') }}</span>
            </button>
          </div>
          <!-- charge end -->
         
          <!-- default card start -->
          <div class="text-center mb-2 default-card-form" , >
            <button type="button" id="default-card-btn" onclick="sendCardData('{{ $result['cardId'] }}')" class="bg-transparent d-flex gap-2 align-items-center justify-content-center w-100 default-card-btn">
              <input type="hidden" name="cardId" value="{{ $result['cardId'] }}">
              <img src="{{ asset('frontend/assets/images/set_default_icon.svg') }}" />
              @if($result['is_default'])
                        <span>{{ __('Card is set as default') }}</span>
                    @else
                        <span>{{ __('Set Card as Default') }}</span>
                    @endif
            </button>
          </div>
          <!-- default card end -->
        </div>
        
        <div class="d-flex align-items-center">
          <!-- transactions start -->
          <a href="{{ route('user.cards.history', ['account_uuid' => $result['account_uuid']]) }}" class="text-center mb-2">
            <button class="bg-transparent d-flex gap-2 align-items-center justify-content-center w-100">
              <img src="{{ asset('frontend/assets/images/transactions_icon.svg') }}" alt="{{ __('Transactions') }}" class="me-2" />
              <span>{{ __('Transactions') }}</span>
            </button>
          </a>
          <!-- transactions end -->
          
          <!-- delete card start -->
          <div class="text-center mb-2">
            <button data-bs-toggle="modal" data-bs-target="#deleteCard" class="bg-transparent d-flex gap-2 align-items-center justify-content-center w-100">
              <img src="{{ asset('frontend/assets/images/delete_icons.svg') }}" alt="{{ __('Delete Card') }}" class="me-2" style="padding-inline-start: 20px;" />
              <span>{{ __('Delete Card') }}</span>
            </button>
          </div>
          <!-- delete card end -->
        </div>
        
        <!-- modals start -->
        
        <!-- card recharge modal-->
        <div class="modal fade" id="cardRecharge" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="cardRechargeLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <form action="{{ route('user.cards.rechargeCard') }}" method="POST">
                @csrf
                <input type="hidden" name="cardId" id="rechargeCardId" value="{{ $result['cardId'] }}">
                <input type="hidden" name="account_uuid" id="rechargeAccountUuid" value="{{ $result['account_uuid'] }}">
    
                <div class="modal-header">
                  <h5 class="modal-title" id="cardRechargeLabel">{{ __('Charge Card') }}</h5>
                  <button style="all: unset; cursor: pointer" type="button" class="btn-close bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
                  </button>
                </div>
                <div class="modal-body modal__body">
                  <div class="form-group">
                    <label for="amount">{{ __('Transfer Amount') }}</label>
                    <input name="amount" type="number" step="0.1" class="form-control text-black-50" id="amount" aria-describedby="amountHelp" placeholder="{{ __('Enter transfer amount') }}" />
                    <span class="text-black-50" id='currency'> USD </span>
                    <div class="modal__body__info d-flex justify-content-between">
                      <p>{{ $virtualCardCharge->min_purchase_limit }} USD - {{ $virtualCardCharge->max_purchase_limit }} USD {{ __('Charge Limit') }}</p>
                      <p>{{ authWalletBalance() }} USD {{ __('Available Balance') }}</p>
                    </div>
    
                    <div class="d-flex justify-content-between recharge_limits">
                      <p>{{ __('Limit') }} {{ $virtualCardCharge->min_purchase_limit }} USD - {{ $virtualCardCharge->max_purchase_limit }} USD</p>
                      <p>{{ __('Charge') }}: {{ $virtualCardCharge->fixed_recharge_fee }} USD + {{ $virtualCardCharge->percent_recharge_fee }}%</p>
                    </div>
                  </div>
                </div>
                <div class="modal-footer d-flex gap-1 justify-content-between">
                  <button type="submit" class="btn btn-primary bg--primary modal__footer__actions confirm d-flex flex-grow-1">
                    {{ __('Confirm') }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- card recharge modal-->
        
        <!-- deleteCard modal-->
        <div class="modal fade" id="deleteCard" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="deleteCardLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <form action="{{ route('user.cards.blockCard') }}" method="POST">
                @csrf        
                <div class="modal-header">
                  <h5 class="modal-title" id="deleteCardLabel">{{ __('Delete Card') }}</h5>
                  <button type="button" style="all: unset" class="btn-close bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('frontend/assets/images/close.svg') }}" height="24" width="24" />
                  </button>
                </div>
                <div class="modal-body modal__body">
                  <h6>{{ __('Are you sure you want to delete this card?') }}</h6>
                  <p>
                    {{ __('If you are sure you will no longer use this card and want to delete it, click "Yes, delete it!"') }}<br />
                    {{ __('Just remember that once deleted, you cannot recover this card or the added information.') }}
                  </p>
                </div>
                <div class="modal-footer d-flex gap-1 justify-content-between">
                  <input type="hidden" name="cardId" id="cardDelete" value="{{ $result['cardId'] }}">
                  <button type="button" class="btn btn-primary modal__footer__actions bg--primary delete_account_modal__footer__actions confirm d-flex flex-grow-1" data-bs-dismiss="modal">
                    {{ __('No, keep my card') }}
                  </button>
                  
                  <button type="submit" class="btn btn-outline-danger modal__footer__actions danger w-25">
                    {{ __('Yes, delete it!') }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <!-- deleteCard modal-->
        
        <!-- modals end -->
      </div>
      <!-- actions end -->
    </div>
    <!-- card section end-->
  </div>
</section>
@endsection

@push('script')

<script>
  $(document).ready(function() {
    
    // eye icon  
    $(document).ready(function() {
      $('.eye-toggle').on('click', function() {
          const span = $(this).siblings('span');
          const currentText = span.text().trim();

          if ($(this).data('visible')) {
              // Hide the number and show stars
              span.text(span.data('masked'));
              $(this).data('visible', false);
          } else {
              // Show the number
              span.data('masked', currentText); // Save the masked version
              span.text(span.data('full'));
              $(this).data('visible', true);
          }
      });
    });

    // flip card 
    $(".virtual_card_actions__rotation_button").click(function () {
      $(this).siblings(".virtual_card_actions__card").find(".flip-card-inner").toggleClass("flipped")
    });
  });

  $('.default-card-btn').on('click', function() {
      // Reset all buttons to their default state
      $('.default-card-btn').each(function() {
          $(this).find('img').css('opacity', '1'); // Make the image fully visible
          $(this).find('span').text('{{ __('Set as Default Card') }}'); // Reset the text
      });

      // Toggle the clicked button
      if ($(this).find('span').text() === '{{ __('Set as Default Card') }}') {
          $(this).find('img').css('opacity', '1'); // Make the image fully visible
          $(this).find('span').text('{{ __('Set as Default Card') }}');
      } else {
          $(this).find('img').css('opacity', '0.5'); // Make the image semi-transparent
          $(this).find('span').text('{{ __('Set as Default Card') }}');
      }
  });

  // Handle status toggle
  $('#statusToggle').on('change', function() {
      let cardId = $(this).data('card-id');
      let statusText = $('#statusText');
      let toggleSwitch = $(this);

      $.ajax({
          url: "{{ route('user.cards.changeStatus') }}",
          type: "POST",
          data: {
              _token: "{{ csrf_token() }}",
              cardId: cardId
          },
          success: function(response) {
              if (response.success) {
                  let newStatus = response.newStatus === 'Active' ? '{{ __('Active') }}' : '{{ __('Inactive') }}';
                  statusText.text(newStatus);
                  toastr.success('{{ __('Status Updated Successfully') }}');
              } else {
                  toggleSwitch.prop('checked', !toggleSwitch.prop('checked')); // Revert the toggle state on failure
                  toastr.error('{{ __('An error occurred. Please try again.') }}');
              }
          },
          error: function(response) {
              toggleSwitch.prop('checked', !toggleSwitch.prop('checked')); // Revert the toggle state on failure
              toastr.error('{{ __('An error occurred. Please try again.') }}');
          }
      });
  });

  // Handle copy logic
  function copyHandler(text, element) {
      // Create a temporary input element to hold the text
      const tempInput = document.createElement('input');
      
      // Set the input's value to the passed text
      tempInput.value = text;
      
      // Append the input to the document's body
      document.body.appendChild(tempInput);
      
      // Select the input's content
      tempInput.select();
      
      // Execute the copy command
      document.execCommand('copy');
      
      // Remove the temporary input element
      document.body.removeChild(tempInput);

      // Change the icon to "Copied!" text
      element.innerHTML = '{{ __("Copied") }}';
      
      // Optionally, change it back to the copy icon after a delay (e.g., 2 seconds)
      setTimeout(() => {
        element.innerHTML = '<img src="{{ asset("frontend/assets/icons/copy.svg") }}" alt="{{ __("copy") }}" />';
      }, 1000);
  }


  function sendCardData(cardId){
    // Create a FormData object
    let formData = new FormData();
    formData.append('cardId', cardId);
    formData.append('_token', "{{ csrf_token() }}");

    // Send the form data via AJAX
    fetch('{{ route('user.cards.makeDefaultOrRemove') }}', {
        method: 'POST',
        body: formData, // Send the FormData object
        headers: {
            'Accept': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
            console.log('{{ __("Card status updated successfully!") }}');
            location.reload(); // Optionally reload the page to reflect changes
    })
    .catch(error => console.error('{{ __("Error:") }}', error));
}
</script>


</script>

@endpush
