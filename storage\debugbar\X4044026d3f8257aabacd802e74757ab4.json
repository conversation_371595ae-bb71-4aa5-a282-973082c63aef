{"__meta": {"id": "X4044026d3f8257aabacd802e74757ab4", "datetime": "2025-06-22 15:16:38", "utime": **********.19992, "method": "POST", "uri": "/register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750583797.722536, "end": **********.199939, "duration": 0.477402925491333, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1750583797.722536, "relative_start": 0, "end": **********.033591, "relative_end": **********.033591, "duration": 0.31105494499206543, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.033602, "relative_start": 0.3110659122467041, "end": **********.199941, "relative_end": 1.9073486328125e-06, "duration": 0.16633892059326172, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26038272, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST register", "middleware": "web", "controller": "App\\Http\\Controllers\\User\\Auth\\RegisterController@register", "as": "user.register.submit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FRegisterController.php&line=61\" onclick=\"\">app/Http/Controllers/User/Auth/RegisterController.php:61-87</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01171, "accumulated_duration_str": "11.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.062561, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 4.27}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 927}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 453}], "start": **********.09163, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "cardapp", "explain": null, "start_percent": 4.27, "width_percent": 4.184}, {"sql": "select * from `users` where `username` = 'yahyaalrefaei' limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1229}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.164396, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1229", "source": {"index": 13, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1229}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1229", "ajax": false, "filename": "helpers.php", "line": "1229"}, "connection": "cardapp", "explain": null, "start_percent": 8.454, "width_percent": 5.295}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'cardapp' and table_name = 'users'", "type": "query", "params": [], "bindings": ["cardapp", "users"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 127}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 82}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.166857, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:127", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FRegisterController.php&line=127", "ajax": false, "filename": "RegisterController.php", "line": "127"}, "connection": "cardapp", "explain": null, "start_percent": 13.749, "width_percent": 41.674}, {"sql": "insert into `users` (`firstname`, `lastname`, `email_verified`, `sms_verified`, `kyc_verified`, `email`, `password`, `username`, `address`, `updated_at`, `created_at`) values ('yahya', 'alre<PERSON><PERSON>', 0, 1, 0, '<EMAIL>', '$2y$10$TSUypEDZW2f6xJCZJShoDudlyleodCGvbtVWRxveV6qnuPO243wTG', 'yahyaalrefaei', '{\\\"country\\\":\\\"\\\",\\\"city\\\":\\\"\\\",\\\"zip\\\":\\\"\\\",\\\"state\\\":\\\"\\\",\\\"address\\\":\\\"\\\"}', '2025-06-22 15:16:38', '2025-06-22 15:16:38')", "type": "query", "params": [], "bindings": ["yahya", "<PERSON><PERSON><PERSON><PERSON>", 0, 1, 0, "<EMAIL>", "$2y$10$TSUypEDZW2f6xJCZJShoDudlyleodCGvbtVWRxveV6qnuPO243wTG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{\"country\":\"\",\"city\":\"\",\"zip\":\"\",\"state\":\"\",\"address\":\"\"}", "2025-06-22 15:16:38", "2025-06-22 15:16:38"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 127}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 82}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.173449, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:127", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FAuth%2FRegisterController.php&line=127", "ajax": false, "filename": "RegisterController.php", "line": "127"}, "connection": "cardapp", "explain": null, "start_percent": 55.423, "width_percent": 20.581}, {"sql": "select `id` from `currencies` where `status` = 1 and ((`sender` = 1) or `receiver` = 1)", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Traits/User/RegisteredUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\RegisteredUsers.php", "line": 12}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 141}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 86}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.182039, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "RegisteredUsers.php:12", "source": {"index": 13, "namespace": null, "name": "app/Traits/User/RegisteredUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\RegisteredUsers.php", "line": 12}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FTraits%2FUser%2FRegisteredUsers.php&line=12", "ajax": false, "filename": "RegisteredUsers.php", "line": "12"}, "connection": "cardapp", "explain": null, "start_percent": 76.003, "width_percent": 4.697}, {"sql": "insert into `user_wallets` (`balance`, `created_at`, `currency_id`, `status`, `user_id`) values (0, '2025-06-22 15:16:38', 1, 1, 3)", "type": "query", "params": [], "bindings": [0, "2025-06-22 15:16:38", 1, 1, 3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/User/RegisteredUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\RegisteredUsers.php", "line": 25}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 141}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/User/Auth/RegisterController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\RegisterController.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.187145, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "RegisteredUsers.php:25", "source": {"index": 14, "namespace": null, "name": "app/Traits/User/RegisteredUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\RegisteredUsers.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FTraits%2FUser%2FRegisteredUsers.php&line=25", "ajax": false, "filename": "RegisteredUsers.php", "line": "25"}, "connection": "cardapp", "explain": null, "start_percent": 80.7, "width_percent": 19.3}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/register", "status_code": "<pre class=sf-dump id=sf-dump-924547755 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-924547755\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1218418305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1218418305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1074527748 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">yahya</span>\"\n  \"<span class=sf-dump-key>lastname</span>\" => \"<span class=sf-dump-str title=\"8 characters\">alrefaei</span>\"\n  \"<span class=sf-dump-key>register_email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>register_password</span>\" => \"<span class=sf-dump-str title=\"7 characters\">GeX@R21</span>\"\n  \"<span class=sf-dump-key>agree</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074527748\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1066782018 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">149</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IlBEdTR6MkpubVJHcXc0cTRDRG5DWEE9PSIsInZhbHVlIjoiTFd1ck44cyt6OEpYZzBuVVFpQ3JEd0VUNHhKTUNkSXJIdnZnUCtueDlPanZQbkF5SHNmaHRPTndMQndnWDJxMzVJejhaZS95L2g1UVkvcVZmaWhEYkMwYk0yNm80WVhsWXRNM3FvWlZOZXVKcWJNc1NobW9TS3pmdHJoWFNNeHQiLCJtYWMiOiJlOTcwN2U0ODllOTQxMGU0Yjc4OWQ2YThjNWQ5MmE0MjIzMWIwOTFmM2IzNGQ1YjMwZTRiMjE4ZjQxMTgwNWEzIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IkdjUy91Z0J2V2ZPNHZVc2hrYVBRcUE9PSIsInZhbHVlIjoiaW1GUTJld0x4aER6SmRWWVRzUmFaM1pyNis0S0V2cVowM0gydnpQUUhURWs3ajVvYTJzaVRsQnY0d3BvVW5CY01IR1F2N0laUDlCcVREK0EwdkR5eVQrZnFjRjFzRFEzTktzMDVuakpORmtiMVprL2s0eHZGYnF6UUcwM2lzeDQiLCJtYWMiOiIxNzhmMmY5YWRhMzY4YmQzYTUwNDY1NTY0NzA3OTIzODg4YmZlYWEzODBkNDkxMzUxMzY3YmIwYmVmNWU0MTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066782018\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1650017189 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RygapEhuNHzIvKDgi3fvYLTxPvSb5kpnIslLDxGP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650017189\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-918792610 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:16:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRRNWY0cjNDK1VqRW8zVGtwemFOTHc9PSIsInZhbHVlIjoiVGpDc1AxR2FVNUJqRldFQVJYVHNIZG1VSjhCQlZXV1R1VjFBaDNMZDJGYlhENWp6dHJSRGQvRFZvNUM1QUhPbmNoci9ZN0dTT1UzWTR4cXhCQVNqdklqRE9reWxnVTJTS2N6eWRIbURHNHdFSmp5ZHRaNFZkc3VGUFJQRjJEVnoiLCJtYWMiOiIzMWMwNDYwZDU3ZGYyYzZmYTM1YzBlY2VhZGNhNjEyNjY4ZWU0MDI3M2Q2MGQwZmU5OGIwNjUwYWY2YzEwYjBjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IjJlbDJiYlJUakRNWjV5VWZvTjBPREE9PSIsInZhbHVlIjoibGhYYWpBZ2RZdm1ObGZNd0NlaENpc0FnZHNUaEgzTWk5N2o1RTFScWgwbnU3Y2xSbDN6TWlIREtrYXBIME5jZEVXemU0VjRsdkRlakgxbFJTOERDTjQ4NkdUMHF4Sko2YXlHNlh4cXh4b3FESmI3dS9QQlh3bTRmVnlmZTlIMzYiLCJtYWMiOiI4NjQ3ZTQ2YjQxYmZlNjRiOGVlYWZhYzQ5YTRjMDdhMDA2ZjhhNjg3OGJkMWJjODkyNWE4MGIyZDU1NWU1NzYwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRRNWY0cjNDK1VqRW8zVGtwemFOTHc9PSIsInZhbHVlIjoiVGpDc1AxR2FVNUJqRldFQVJYVHNIZG1VSjhCQlZXV1R1VjFBaDNMZDJGYlhENWp6dHJSRGQvRFZvNUM1QUhPbmNoci9ZN0dTT1UzWTR4cXhCQVNqdklqRE9reWxnVTJTS2N6eWRIbURHNHdFSmp5ZHRaNFZkc3VGUFJQRjJEVnoiLCJtYWMiOiIzMWMwNDYwZDU3ZGYyYzZmYTM1YzBlY2VhZGNhNjEyNjY4ZWU0MDI3M2Q2MGQwZmU5OGIwNjUwYWY2YzEwYjBjIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IjJlbDJiYlJUakRNWjV5VWZvTjBPREE9PSIsInZhbHVlIjoibGhYYWpBZ2RZdm1ObGZNd0NlaENpc0FnZHNUaEgzTWk5N2o1RTFScWgwbnU3Y2xSbDN6TWlIREtrYXBIME5jZEVXemU0VjRsdkRlakgxbFJTOERDTjQ4NkdUMHF4Sko2YXlHNlh4cXh4b3FESmI3dS9QQlh3bTRmVnlmZTlIMzYiLCJtYWMiOiI4NjQ3ZTQ2YjQxYmZlNjRiOGVlYWZhYzQ5YTRjMDdhMDA2ZjhhNjg3OGJkMWJjODkyNWE4MGIyZDU1NWU1NzYwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:16:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918792610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2075356896 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075356896\", {\"maxDepth\":0})</script>\n"}}