@extends('user.layouts.master')

@push('css')
<link rel="stylesheet" href="{{ asset('frontend/css/custom/all_created_cards.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __($page_title)])
@endsection

@section('content')
<span class="d-flex justify-content-between header">
      <h6 class="header_title">{{ __('Virtual Cards') }}</h6>
    </span>
<div class="container">
    <div class="row">
        <!-- Card 1 -->
        @foreach($cards as $card)
        <div class="col-md-12 col-lg-6 col-xl-4 mb-4 cursor-pointer" 
             data-bs-toggle="modal" 
             data-bs-target="#addToCardsModal" 
             data-bin="{{ $card->bin }}" 
             data-card-id="{{ $card->id }}">
            <div class="border border--primary rounded-3 p-2 one_card text-dark">
                <!-- Card Content -->
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center position-relative">
                        @php
                            $bin = substr($card->bin, 0, 8);
                            $cardImage = $cardImages->firstWhere('bin', $bin);
                            $imagePath = $cardImage ? $cardImage->image_path : 'default/path/to/image.png';
                        @endphp
                        <img src="{{ asset($imagePath) }}" 
                             alt="card" 
                             class="img-fluid" 
                             style="height: 53px; width: 85px;">
                        <div class="p-2">
                            <p class="mb-1 fw-bold text-size-14 card__name__color">{{ $card->type }}</p>
                            <p class="mb-0 fw-light text-size-12">{{ $card->bin }}</p>
                        </div>
                        <div class='position-absolute fw-light text-size-12 z-50 text-white px-3'>
                            <p class='card__num'>{{ $card->bin }}</p>
                            <div class='d-flex justify-content-between align-items-center card__name__logo'>
                                <img src="{{ asset('frontend/assets/vcards/master_card.svg') }}" 
                                     alt="mastercard" 
                                     style="height: 20px; width: 20px;">
                            </div>
                        </div>
                    </div>
                    <img src="{{ asset('frontend/assets/down-gray-arrow.svg') }}" 
                         alt="arrow" 
                         class="rotate_arrow img-fluid" 
                         style="height: 20px; width: 20px;">
                </div>
            </div>
        </div>
        @endforeach    
    
   
        <!-- Add more cards as needed -->

    </div>



    <!-- modal for each card -->
    <div class="modal fade" id="addToCardsModal" tabindex="-1" aria-labelledby="addToCardsModalLabel" aria-hidden="true" style="z-index: 9999;">
      <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
              <form action="{{ route('user.cards.add_new_card') }}" method="POST">
                  @csrf
                  <div class="modal-header">
                      <h5 class="modal-title" id="addToCardsModalLabel">اختر البطاقة</h5>
                      <input type="hidden" name="bin" id="modalBin">
                      <button type="button" class="btn-close bg-transparent" data-bs-dismiss="modal" aria-label="Close">
                          <img src="{{ asset('frontend/assets/icons/X.svg') }}" height="24" width="24" />
                      </button>
                  </div>
                  <div class="modal-body">
                      <h5 class="text-dark">هل أنت متأكد من رغبتك في امتلاك هذة البطاقة الإفتراضية؟</h5>
                  </div>
                  <div class="modal-footer d-flex justify-content-center">
                      <button type="submit" class="btn btn--base">نعم اضفها لبطاقاتي</button>
                      <button type="button" class="btn secondary__btn__base" data-bs-dismiss="modal" aria-label="Close">لا, العودة</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
    <!-- end of modal -->



</div>
@endsection

@push('script')
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const cards = document.querySelectorAll('[data-bs-toggle="modal"]');
    cards.forEach(card => {
        card.addEventListener('click', function () {
            const bin = this.getAttribute('data-bin');
            document.getElementById('modalBin').value = bin;
        });
    });
});

  </script>
<script src="{{ asset('frontend/js/custom/allCreatedCards.js') }}"></script>
@endpush
