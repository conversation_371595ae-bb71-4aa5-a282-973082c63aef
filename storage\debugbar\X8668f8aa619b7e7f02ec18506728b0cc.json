{"__meta": {"id": "X8668f8aa619b7e7f02ec18506728b0cc", "datetime": "2025-06-22 15:17:23", "utime": **********.650173, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:17:23] LOG.warning: mb_strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 508", "message_html": null, "is_string": false, "label": "warning", "time": **********.540727, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.229267, "end": **********.650197, "duration": 0.****************, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.229267, "relative_start": 0, "end": **********.496522, "relative_end": **********.496522, "duration": 0.2672550678253174, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.496534, "relative_start": 0.26726722717285156, "end": **********.650209, "relative_end": 1.1920928955078125e-05, "duration": 0.15367484092712402, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26255664, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\User\\Auth\\LoginController@login", "as": "user.login.submit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fui%2Fauth-backend%2FAuthenticatesUsers.php&line=32\" onclick=\"\">vendor/laravel/ui/auth-backend/AuthenticatesUsers.php:32-60</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00958, "accumulated_duration_str": "9.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.522096, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 8.038}, {"sql": "select * from `users` where `email` = '<EMAIL>' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 18, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.544955, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "cardapp", "explain": null, "start_percent": 8.038, "width_percent": 7.411}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 3 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 14}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 121}, {"index": 21, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 22, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.621139, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "LoggedInUsers.php:14", "source": {"index": 19, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FTraits%2FUser%2FLoggedInUsers.php&line=14", "ajax": false, "filename": "LoggedInUsers.php", "line": "14"}, "connection": "cardapp", "explain": null, "start_percent": 15.449, "width_percent": 9.186}, {"sql": "select `id` from `currencies` where `status` = 1 and ((`sender` = 1) or `receiver` = 1)", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 15}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 121}, {"index": 15, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 16, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.625537, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "LoggedInUsers.php:15", "source": {"index": 13, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FTraits%2FUser%2FLoggedInUsers.php&line=15", "ajax": false, "filename": "LoggedInUsers.php", "line": "15"}, "connection": "cardapp", "explain": null, "start_percent": 24.635, "width_percent": 9.29}, {"sql": "insert into `user_login_logs` (`user_id`, `ip`, `mac`, `city`, `country`, `longitude`, `latitude`, `timezone`, `browser`, `os`, `updated_at`, `created_at`) values (3, '127.0.0.1', '', 'New Haven', 'United States', -72.92, 41.31, 'America/New_York', 'Chrome', 'Windows', '2025-06-22 15:17:23', '2025-06-22 15:17:23')", "type": "query", "params": [], "bindings": [3, "127.0.0.1", "", "New Haven", "United States", -72.92, 41.31, "America/New_York", "Chrome", "Windows", "2025-06-22 15:17:23", "2025-06-22 15:17:23"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/User/Auth/LoginController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\Auth\\LoginController.php", "line": 122}, {"index": 23, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 24, "namespace": null, "name": "vendor/laravel/ui/auth-backend/AuthenticatesUsers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 51}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.632824, "duration": 0.00633, "duration_str": "6.33ms", "memory": 0, "memory_str": null, "filename": "LoggedInUsers.php:57", "source": {"index": 21, "namespace": null, "name": "app/Traits/User/LoggedInUsers.php", "file": "D:\\projects\\CardApp\\app\\Traits\\User\\LoggedInUsers.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FTraits%2FUser%2FLoggedInUsers.php&line=57", "ajax": false, "filename": "LoggedInUsers.php", "line": "57"}, "connection": "cardapp", "explain": null, "start_percent": 33.925, "width_percent": 66.075}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUserWallet.php&line=1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Admin\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "local": "ar", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-143853207 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-143853207\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1564426404 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1564426404\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1008372669 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>credentials</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1008372669\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-403360871 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">94</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6Ii8rY3hGa2lOWC9KQ0x1cFNzTmNSQVE9PSIsInZhbHVlIjoiQ0VFYVUyR1BqN29UZGxSWng2TlAzNWcrbVl5ZFJpN1N1Y0ZjeVRpT1pPNlZqMGVaS2tvQng3R0tBNis4SS82NllvcHNnYk84d2pwTjJYbUhKUnBWL2tsZi9EWFlUdWZyRWR1bTlORVlMbzN6SmtQSEhJWm85VEdIdzl1NmsrRVciLCJtYWMiOiI2ZWMxZjA2MDE4YjRhMTE2MmZjZjg0MjIxNDBkYjYwY2Y0MTJhNmYwMzU1NzE1OTA0ODUzODYzNjJlNGJmYjhjIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IkdkUER5bCs4VjZ2Q0NGRFdWaGNXM2c9PSIsInZhbHVlIjoiKzVFZE1sZ3lsUDcwTExRVlFwNDdTaC9ZbDJFa200dG1GQm5aOXZIekRUQzF5bkNMSjZ3TmIvOXBWL043UnJYcDRnVWxhcFlIaHJsTUprZS9ZUFFKeW43UnlQSUJzRnl2NUs1SGEvVjdTRUxhNXJlUzNkWDJzbmNFbEt5dmdvemoiLCJtYWMiOiJjZDJkYTU0NDQ3NTY0OTFlYTFmMTcwNTYzYzdiNGYzNWRhNWJlMGFjMWYxN2U1MDk3OTgzNTZlMTliZjYxYThiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-403360871\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1288086434 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6hJB8uO6hRqblWYRUHX84avzRVBLyhWhjPxEukEh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288086434\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1507196465 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:17:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImFqOXQwMm00VUFvRVhBVVJmVGszaWc9PSIsInZhbHVlIjoieHpBb0lZTUx3c203K3d6ZG9NVEZnMVRVUlZHRSs2NUU2RG50clZIYXJpaHlPQ2tTQ2JIZXZML2tUQndldWpDVURwU3RpRjdmelRYWFBud3FsSWJYcHBiV3VCRHdUQmZEc3hSd3Mvak0wam9TUjJreXg0R3JtS1Z0amlvSHlTMDciLCJtYWMiOiIzMDRjZjE2ZjEyZjRmZGYyNjM1YTNjZjY1MTY1NzU1YzVhYWNlNWQ3OTM3OGRhNTBmZDIzMDY1NmI0Yzc2YTI1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IlZFeHRNL1lWMlZhTC9SckFmV1BEOWc9PSIsInZhbHVlIjoicUFveXpKYXhoSkJRaTkxU1pVL1FMM2o3QzdHTEk0VUxEOW1YcWFLdVJ5QVR1bVhXbmQ0a3FadGxFdEwzODVCQXNLV0R3dkVHenVFdmRoaWwrR0lqUlFJZVU2OUhydEQvdE0vY1NtK3doT2FIUE1RZnppM24ydlBPNzBnS05yR1AiLCJtYWMiOiI5N2ZkZjg0NThkMDJhYzVkOTBjNDEwZGEyNjk5YzMxNjQ4YTcxM2Y3NjFlYjU4MTA3ZTg3N2EzYTJjMzU4NTQwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImFqOXQwMm00VUFvRVhBVVJmVGszaWc9PSIsInZhbHVlIjoieHpBb0lZTUx3c203K3d6ZG9NVEZnMVRVUlZHRSs2NUU2RG50clZIYXJpaHlPQ2tTQ2JIZXZML2tUQndldWpDVURwU3RpRjdmelRYWFBud3FsSWJYcHBiV3VCRHdUQmZEc3hSd3Mvak0wam9TUjJreXg0R3JtS1Z0amlvSHlTMDciLCJtYWMiOiIzMDRjZjE2ZjEyZjRmZGYyNjM1YTNjZjY1MTY1NzU1YzVhYWNlNWQ3OTM3OGRhNTBmZDIzMDY1NmI0Yzc2YTI1IiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IlZFeHRNL1lWMlZhTC9SckFmV1BEOWc9PSIsInZhbHVlIjoicUFveXpKYXhoSkJRaTkxU1pVL1FMM2o3QzdHTEk0VUxEOW1YcWFLdVJ5QVR1bVhXbmQ0a3FadGxFdEwzODVCQXNLV0R3dkVHenVFdmRoaWwrR0lqUlFJZVU2OUhydEQvdE0vY1NtK3doT2FIUE1RZnppM24ydlBPNzBnS05yR1AiLCJtYWMiOiI5N2ZkZjg0NThkMDJhYzVkOTBjNDEwZGEyNjk5YzMxNjQ4YTcxM2Y3NjFlYjU4MTA3ZTg3N2EzYTJjMzU4NTQwIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:17:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507196465\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-236175180 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z6W5XPdpC7oOGeT3uVLH8M3WazqGd6QZm79adT2M</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>local</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236175180\", {\"maxDepth\":0})</script>\n"}}