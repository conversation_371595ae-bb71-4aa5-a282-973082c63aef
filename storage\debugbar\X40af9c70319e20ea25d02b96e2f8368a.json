{"__meta": {"id": "X40af9c70319e20ea25d02b96e2f8368a", "datetime": "2025-06-22 15:50:20", "utime": **********.480078, "method": "GET", "uri": "/user/transfer-money", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.129021, "end": **********.480102, "duration": 0.35108113288879395, "duration_str": "351ms", "measures": [{"label": "Booting", "start": **********.129021, "relative_start": 0, "end": **********.392177, "relative_end": **********.392177, "duration": 0.2631561756134033, "duration_str": "263ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.392191, "relative_start": 0.2631700038909912, "end": **********.480104, "relative_end": 1.9073486328125e-06, "duration": 0.08791303634643555, "duration_str": "87.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26060784, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "user.sections.transfer-money.index", "param_count": null, "params": [], "start": **********.452893, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/sections/transfer-money/index.blade.phpuser.sections.transfer-money.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fsections%2Ftransfer-money%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "user.components.breadcrumb", "param_count": null, "params": [], "start": **********.461894, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/breadcrumb.blade.phpuser.components.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "user.components.transaction-log", "param_count": null, "params": [], "start": **********.46365, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/components/transaction-log.blade.phpuser.components.transaction-log", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fcomponents%2Ftransaction-log.blade.php&line=1", "ajax": false, "filename": "transaction-log.blade.php", "line": "?"}}, {"name": "user.layouts.master", "param_count": null, "params": [], "start": **********.467645, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/layouts/master.blade.phpuser.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.header-asset", "param_count": null, "params": [], "start": **********.469832, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/header-asset.blade.phppartials.header-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Fheader-asset.blade.php&line=1", "ajax": false, "filename": "header-asset.blade.php", "line": "?"}}, {"name": "user.partials.side-nav", "param_count": null, "params": [], "start": **********.472623, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/partials/side-nav.blade.phpuser.partials.side-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Fside-nav.blade.php&line=1", "ajax": false, "filename": "side-nav.blade.php", "line": "?"}}, {"name": "user.partials.top-nav", "param_count": null, "params": [], "start": **********.473805, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.phpuser.partials.top-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Ftop-nav.blade.php&line=1", "ajax": false, "filename": "top-nav.blade.php", "line": "?"}}, {"name": "partials.footer-asset", "param_count": null, "params": [], "start": **********.476459, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/footer-asset.blade.phppartials.footer-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Ffooter-asset.blade.php&line=1", "ajax": false, "filename": "footer-asset.blade.php", "line": "?"}}, {"name": "admin.partials.notify", "param_count": null, "params": [], "start": **********.476941, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/notify.blade.phpadmin.partials.notify", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}]}, "route": {"uri": "GET user/transfer-money", "middleware": "web, auth, verification.guard", "controller": "App\\Http\\Controllers\\User\\TransferMoneyController@index", "as": "user.transfer.money.index", "namespace": null, "prefix": "user/transfer-money", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=26\" onclick=\"\">app/Http/Controllers/User/TransferMoneyController.php:26-32</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0085, "accumulated_duration_str": "8.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.4174302, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 7.294}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.420891, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 7.294, "width_percent": 7.882}, {"sql": "select * from `transaction_settings` where `slug` = 'transfer-money' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["transfer-money", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.426626, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:28", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=28", "ajax": false, "filename": "TransferMoneyController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 15.176, "width_percent": 6.588}, {"sql": "select * from `admins` where `admins`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.430664, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:28", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=28", "ajax": false, "filename": "TransferMoneyController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 21.765, "width_percent": 4.588}, {"sql": "select * from `admin_has_roles` where `admin_has_roles`.`admin_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.433215, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:28", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=28", "ajax": false, "filename": "TransferMoneyController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 26.353, "width_percent": 17.529}, {"sql": "select * from `admin_roles` where `admin_roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.436135, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:28", "source": {"index": 30, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=28", "ajax": false, "filename": "TransferMoneyController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 43.882, "width_percent": 4.588}, {"sql": "select * from `admin_role_permissions` where `admin_role_permissions`.`admin_role_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.438262, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:28", "source": {"index": 30, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=28", "ajax": false, "filename": "TransferMoneyController.php", "line": "28"}, "connection": "cardapp", "explain": null, "start_percent": 48.471, "width_percent": 14.824}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 3 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.44112, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:29", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=29", "ajax": false, "filename": "TransferMoneyController.php", "line": "29"}, "connection": "cardapp", "explain": null, "start_percent": 63.294, "width_percent": 7.059}, {"sql": "select * from `transactions` where `user_id` = 3 and `type` = 'TRANSFER-MONEY' order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [3, "TRANSFER-MONEY"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 30}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4435031, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TransferMoneyController.php:30", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/User/TransferMoneyController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\User\\TransferMoneyController.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FUser%2FTransferMoneyController.php&line=30", "ajax": false, "filename": "TransferMoneyController.php", "line": "30"}, "connection": "cardapp", "explain": null, "start_percent": 70.353, "width_percent": 5.176}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.468268, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 75.529, "width_percent": 6.118}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.4706821, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 81.647, "width_percent": 9.412}, {"sql": "select * from `transactions` where `user_id` = 3 order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "user.partials.top-nav", "file": "D:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.php", "line": 2}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.474462, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "user.partials.top-nav:2", "source": {"index": 14, "namespace": "view", "name": "user.partials.top-nav", "file": "D:\\projects\\CardApp\\resources\\views/user/partials/top-nav.blade.php", "line": 2}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fuser%2Fpartials%2Ftop-nav.blade.php&line=2", "ajax": false, "filename": "top-nav.blade.php", "line": "2"}, "connection": "cardapp", "explain": null, "start_percent": 91.059, "width_percent": 8.941}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\TransactionSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FTransactionSetting.php&line=1", "ajax": false, "filename": "TransactionSetting.php", "line": "?"}}, "App\\Models\\Admin\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "App\\Models\\Admin\\AdminHasRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminHasRole.php&line=1", "ajax": false, "filename": "AdminHasRole.php", "line": "?"}}, "App\\Models\\Admin\\AdminRole": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FAdminRole.php&line=1", "ajax": false, "filename": "AdminRole.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FUserWallet.php&line=1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nR5KChHYFN63CZCEXZVuVY79rGdg1HTNOfKtvoN5", "_flash": "array:2 [\n  \"new\" => []\n  \"old\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/user/transfer-money\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "3", "auth": "array:1 [\n  \"password_confirmed_at\" => 1750585814\n]"}, "request": {"path_info": "/user/transfer-money", "status_code": "<pre class=sf-dump id=sf-dump-1413954527 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1413954527\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-37114774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-37114774\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1603315255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1603315255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1365458744 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2338 characters\">approval_status=eyJpdiI6ImdJQmJsWUN5Y0RiYXhrdDE4dlBMOVE9PSIsInZhbHVlIjoiUzgxQmEvN2I2aVFMWDhGNE8yb3dMbitWbENTZEhZaGU0QVhLUy9CQWpteWc1SXErYXZYb3NpUGFFTHFvMnBrSCIsIm1hYyI6ImI4N2FjZDY3MzZjNjRkMGUwZGI4NDZhNjlkZjNmMzM4MzMzMzg4OGI2OTA2ZDc1NWE0MDZmYzA4MjMwYmQ5YTIiLCJ0YWciOiIifQ%3D%3D; user_agent=eyJpdiI6InBwUFhFNmEvWHcyZkVFcGRqQlpHREE9PSIsInZhbHVlIjoiYXBud2NLamZTZHFBR2hMRkxVck1PRDcxd3VrcS9jMjBZUTA3TnRYTHdsL0lJQ0NrbENCdU1PTzBLQnBOa3R5ZzB0TDZUSHlid1RvbFp6QnZObGxwUVVlbWhGZ2xEaDhGNzlEdkxlZXB4S1k5K2xTVlZaNmp0MFNBQkxKSTBiVTd2NVNjNkUrMGVCWml2YStVblhwbldWdjVXVDI1RUF2dnZPL1Yxek1qZHRSc3NyWE5GMkdoWUd3ZU8veWpDS1NRekhuUFRDSE1ldXBGYm10M1BXblROUT09IiwibWFjIjoiMTdlODhlNmFjNjE4MDc4MDZkY2Y2NDEyNzk4MDI0Mjc0Nzg0YTlkNzY3YWI4Y2QwMDA1ZDNhOWE0MTA5ZTE5ZSIsInRhZyI6IiJ9; ip_address=eyJpdiI6InNuN3pwTWl4ZFQrV1JmemR1UUtIOGc9PSIsInZhbHVlIjoiZWk0M2g4NmZ3YVBra3dkS3FreU9wRWcyUEtCR2tGc1ZJWk5yR2xkSEE3S0UvdmN1Y0VhYnRZdzBPN1dWTHJyV1I2blNteGl2OEp5RVV3L1JiVXMzY0E9PSIsIm1hYyI6ImFiYmRhYTVhZDQ1MDkwZGYxNmI3MmQwMmZlYjU4MTQzMDg3Yjg5MGM3MGUyNDJmOWFjOGQ3OWQzYWI5ZDUwZTMiLCJ0YWciOiIifQ%3D%3D; browser=eyJpdiI6IjhFV3B5SGlJeEQ5Zks0RE40WHJOYlE9PSIsInZhbHVlIjoiMENSSGFkNzlnNEhpNXNEOHFodlFEOEtMTXNSYzBwV0dvaG9jOVlGSzRyZ1RQdHJDQUxrelAwTjRvN0x4YnlxWiIsIm1hYyI6IjIxZjNmZGM0OTU1MzZhYmNiMTU0Mzg3N2FmNjc1ZGI4NGQ0MjBhOGE5OWExYmZiOTA4YjQ1OTUyZDJkZmU3MmEiLCJ0YWciOiIifQ%3D%3D; platform=eyJpdiI6IjZ5a0JubVJPcDZiOFFnZklGQTBkWUE9PSIsInZhbHVlIjoiU1paTS9ZSjlmOG5pNVRRWlpJb09tVGh3empSRnRtbDhsKzBUOE9nSlhnejNvcFFIR25MdHI4SVpwbGEzZ2h6QXE4SDRKbUZnZ1lGb2pJNmZmN0EzSUE9PSIsIm1hYyI6ImVlM2FjNjIzOTNmZTQ1NzliYjBiOGVmZjUzMzM5ZTFmOTZlYjAzZjNlMTdjZDRjMmVmZjE2ZDJkNjcyZTcyYzkiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IjJOM2lEeWplUDMybWphVU84ZjRtK1E9PSIsInZhbHVlIjoiQnM2Q2hCRHhmZVlpKzdJemYxeEh5ZDNVTC96S0sraXk4KzRHbFkyL3ZSNVVTL1ZhOUNCN1pzMTJmVTFZNnh2QTc0b0gyT2NKSGJQeW9nL3JWOVVOTHdYeXlnWnpGZ1dkdGN4UHlhWmcyVlFZekF4SGNqZWR3N0FJYnl3d1c2L24iLCJtYWMiOiJjNzIwZTc1ZGEyY2Y5NjI1YzQxNmIyYTcwNTM5Y2JkYzY5OWY4N2RkZTIwMTk2MmU4MTRjZTQxNWViNTgxNDJjIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6IjNWenBDd0VBRG52VGVLczB5S0F2MFE9PSIsInZhbHVlIjoidnBMV24xWUZQRDBCUVVQZXh6b1RTanUvMS80Ry9pY2hCVDIvN3F5WnFrKzJ4WGM5bnlnMTF6THY5L0ltSCtZM3JWaEZDaU4yOStRa0hSQmp4NHl0UzFsWWlBSFJ2dEYvdXVHeXdxbEE4MTRteVJaWjgweTkxNXVFNkpUeEhkTHciLCJtYWMiOiI1ODViY2MwODg2YTJiNTkwNmYzOGIyZTM0ZmM4ZWUzYTE0YzUxNzU5OGM4ZTg2OTJiOTFmMzgwOWRmMTM0YThlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365458744\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-96097598 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>approval_status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">allow</span>\"\n  \"<span class=sf-dump-key>user_agent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>ip_address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>browser</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Chrome</span>\"\n  \"<span class=sf-dump-key>platform</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Windows</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nR5KChHYFN63CZCEXZVuVY79rGdg1HTNOfKtvoN5</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FbXjCMTAocEY1PpWbxStbBPvt33hSdNjQEClpIld</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96097598\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1508962187 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 09:50:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjZJZzlNeU0xMHlnYnMybFQ2TEkyQ1E9PSIsInZhbHVlIjoic29HZzNJTW5wU3h1R0VEMHlXTW95N215WUhsSUcyZStDU0RpK2VCZDdSN2toS3JNaW1leDQzekFpTTlNR2dUcERXYTNXeGMrNnZ3VHZEU3JtUnVPR2g1cHIzWjNIOG9PUWIwbTIwMXlRbnkrS3lTQlE1TVBsY0tyWWlGQ3FSNHYiLCJtYWMiOiI1NWFlMDg2YzNmY2ViYjA0ZDg4MjJhNTM4Mjg0MGNhZjk0MDQ4ZjA5ZGM2YzMyNTFhMzJmMjllN2YyMTVlMTdmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:50:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IlNkaC9GdlhaUXVtdEVaSVZoMGlUeXc9PSIsInZhbHVlIjoiU09uSlFLeVFPUFZIMHVPZ0NJOXluQjlBRGZnTEFRTWxHb2xPTWNZSnllOHQyVTg3Y0lzQnJab1UzamlweUl0YTVvRXNQS0dONjcvRkYxUkk0VWVjYk9rNnJ2SldLUWE4THFtR1hqZXlOZzgvZ3l0c3dkTVc4Y25qeHoxWEJiQmIiLCJtYWMiOiI5NzU1YTliMDhlNmNkMWVlZmJjZjlmMmFiNzhjNjYxODlhMmUxOGViYjkwYzgzNmViYjRmNGFiYjRjNmUxNDdmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:50:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjZJZzlNeU0xMHlnYnMybFQ2TEkyQ1E9PSIsInZhbHVlIjoic29HZzNJTW5wU3h1R0VEMHlXTW95N215WUhsSUcyZStDU0RpK2VCZDdSN2toS3JNaW1leDQzekFpTTlNR2dUcERXYTNXeGMrNnZ3VHZEU3JtUnVPR2g1cHIzWjNIOG9PUWIwbTIwMXlRbnkrS3lTQlE1TVBsY0tyWWlGQ3FSNHYiLCJtYWMiOiI1NWFlMDg2YzNmY2ViYjA0ZDg4MjJhNTM4Mjg0MGNhZjk0MDQ4ZjA5ZGM2YzMyNTFhMzJmMjllN2YyMTVlMTdmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:50:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IlNkaC9GdlhaUXVtdEVaSVZoMGlUeXc9PSIsInZhbHVlIjoiU09uSlFLeVFPUFZIMHVPZ0NJOXluQjlBRGZnTEFRTWxHb2xPTWNZSnllOHQyVTg3Y0lzQnJab1UzamlweUl0YTVvRXNQS0dONjcvRkYxUkk0VWVjYk9rNnJ2SldLUWE4THFtR1hqZXlOZzgvZ3l0c3dkTVc4Y25qeHoxWEJiQmIiLCJtYWMiOiI5NzU1YTliMDhlNmNkMWVlZmJjZjlmMmFiNzhjNjYxODlhMmUxOGViYjkwYzgzNmViYjRmNGFiYjRjNmUxNDdmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 11:50:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508962187\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1929850548 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nR5KChHYFN63CZCEXZVuVY79rGdg1HTNOfKtvoN5</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/user/transfer-money</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1750585814</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929850548\", {\"maxDepth\":0})</script>\n"}}