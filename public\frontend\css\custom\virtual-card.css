
.card-custom {
    width: 380px;
    height: 260px;
    cursor: pointer;
}
@media only screen and (max-width: 991px) {
    .card-custom {
        width: 430px;
        height: 270px;
    }
    .card-custom-area{
        padding-bottom: 30px;
    }
}
@media only screen and (max-width: 576px) {
    .dashboard-area .title {
        font-weight: 600;
        font-size: 25px;
        padding-bottom: 10px;
    }
    .card-custom {
        width: 350px;
    }
}
@media only screen and (max-width: 370px) {
    .card-custom {
        width: 300px;
    }
}
.flip {
width: inherit;
height: inherit;
transition: 0.7s;
transform-style: preserve-3d;
}
.front,
.back {
position: absolute;
width: inherit;
height: inherit;
border-radius: 15px;
color: #fff;
text-shadow: 0 1px 1px rgba(0,0,0,0.3);
box-shadow: 0 1px 10px 1px rgba(0,0,0,0.3);
backface-visibility: hidden;
background-image: linear-gradient(to right, #111, #555);
overflow: hidden;
}
.front {
transform: translateZ(0);
}
.logo {
    position: absolute;
    top: 20px;
    right: 25px;
    width: 111px;
    height: 34px;
}
.investor {
    position: relative;
    top: 28px;
    left: 25px;
    text-transform: uppercase;
    text-align: left;
}
.chip {
position: relative;
top: 60px;
left: 25px;
display: flex;
align-items: center;
justify-content: center;
width: 50px;
height: 40px;
border-radius: 5px;
background-image: linear-gradient(to bottom left, #ffecc7, #d0b978);
overflow: hidden;
}
@media only screen and (max-width: 375px) {
    .chip {
        top: 52px;
        left: 25px;
        width: 48px;
        height: 37px;
    }
  }
.chip .chip-line {
position: absolute;
width: 100%;
height: 1px;
background-color: #333;
}
.chip .chip-line:nth-child(1) {
top: 13px;
}
.chip .chip-line:nth-child(2) {
top: 20px;
}
.chip .chip-line:nth-child(3) {
top: 28px;
}
.chip .chip-line:nth-child(4) {
left: 25px;
width: 1px;
height: 50px;
}
.chip .chip-main {
width: 20px;
height: 25px;
border: 1px solid #333;
border-radius: 3px;
background-image: linear-gradient(to bottom left, #efdbab, #e1cb94);
z-index: 1;
}
.wave {
position: relative;
top: 20px;
left: 100px;
}
.card-number {
position: relative;
display: flex;
justify-content: space-between;
align-items: center;
margin: 40px 25px 10px;
font-size: 23px;
font-family: 'cc font', monospace;
}
@media only screen and (max-width: 375px) {
    .card-number {
      font-size: 16px;
    }
  }
.end {
margin-left: 27px;
text-transform: uppercase;
font-family: 'cc font', monospace;
text-align: left;
}
.end .end-text {
font-size: 9px;
color: rgba(255,255,255,0.8);
}
.card-holder {
margin: 10px 27px;
text-align: left;
text-transform: uppercase;
font-family: 'cc font', monospace;
}
.master {
position: absolute;
right: 20px;
bottom: 20px;
display: flex;
}
.master img{
    height: 15px !important;
    width: auto !important;
}
@media only screen and (max-width: 580px) {
    .master{
        bottom: 44px;
    }
  }
.master .circle {
width: 25px;
height: 25px;
border-radius: 50%;
}
.master .master-red {
background-color: #eb001b;
}
.master .master-yellow {
margin-left: -10px;
background-color: rgba(255,209,0,0.7);
}
.card-custom {
perspective: 1000;
}
.card-custom.active .flip {
transform: rotateY(180deg);
}
.back {
    transform: rotateY(180deg) translateZ(0);
    background: #3A3773;
}
.back .strip-black {
position: absolute;
top: 30px;
left: 0;
width: 100%;
height: 50px;
background: #000;
}
.back .ccv {
position: absolute;
top: 110px;
left: 0;
right: 0;
height: 36px;
width: 90%;
padding: 10px;
margin: 0 auto;
border-radius: 5px;
text-align: right;
letter-spacing: 1px;
color: #000;
background: #fff;
}
.back .ccv label {
display: block;
margin: -40px 0 15px;
font-size: 10px;
text-transform: uppercase;
color: #fff;
}
.back .terms {
position: absolute;
top: 150px;
padding: 20px;
font-size: 10px;
text-align: justify;
}
@-moz-keyframes flip {
0%, 100% {
    transform: rotateY(0deg);
}
50% {
    transform: rotateY(180deg);
}
}
@-webkit-keyframes flip {
0%, 100% {
    transform: rotateY(0deg);
}
50% {
    transform: rotateY(180deg);
}
}
@-o-keyframes flip {
0%, 100% {
    transform: rotateY(0deg);
}
50% {
    transform: rotateY(180deg);
}
}
@keyframes flip {
0%, 100% {
    transform: rotateY(0deg);
}
50% {
    transform: rotateY(180deg);
}
}

/*  */
.virtual_card_actions{
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    border: 1px solid #D0CEFF;
    border-radius:0.625rem ;
    box-shadow: 0 4px 7.3px 0 #635BFE14;

}
.header{
    margin-bottom: 1.5rem ;
}
.header_title{
    font-size: 16px;
    font-weight: 700;
    color: black;
}
.flip-card {
    background-color: transparent;
    width: 311px;
    height: 194px;
    border: 1px solid #f1f1f1;
    perspective: 1000px;
    border-radius: 18px;
}

/* This container is needed to position the front and back side */
.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}


.flip-card-inner.flipped {
    transform: rotateY(180deg);

}
/* Position the front and back side */
.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}


.flip-card-front {
    /* background-image:url("/frontend/assets/images/card_front_background.svg") ; */
    background-size: auto;
    background-repeat: no-repeat;
    background-position: top ;
    color: black;

}

.card-front-inner{
    height: 100%;
    width: 100%;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background-repeat: no-repeat;
    background-size: contain;
    


}
/* Style the back side */
.flip-card-back {
    background-image:url("/frontend/assets/images/card_back_background.svg") ;
    background-size: auto;
    background-repeat: no-repeat;
    background-position: top ;
    color: black;
    transform: rotateY(180deg);
    position: relative;
}
.ccv_number{
    position: absolute;
    top: 55%;
    left: 15%;
    transform: translate(-50%,-50%);
}
.card_number{
    font-family:"Neue Machina";
    font-size: 24px;
    font-weight: 400;
}
.card_exp{

    font-size: 12px;
    font-weight: 800;
}
.virtual_card_actions__actions__action p{
    font-size: 16px;
    font-weight: 500;
    color: #424242;
}
.modal-content{
    border-radius: 0.625rem;
    border-color: transparent !important;
}
.modal-header{
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    background-color: #E6E6E6;
    padding: 0.5rem;
    border-top-left-radius: 0.625rem;
    border-top-right-radius: 0.625rem;
    border-color: transparent;

}
.modal-title{
    font-size: 14px;
    font-weight: 700;
    color: #555555;
}
.btn-close:focus ,.btn-close:active{
    box-shadow: unset;
}

.modal-body ,.modal-footer{
    background-color: #F7F7F7;
}
.modal-footer{
    padding: 0 1rem 1rem;
    border: none;
    border-bottom-left-radius: 0.625rem;
    border-bottom-right-radius: 0.625rem;
}

.reset_password_modal__eye_icon{
    background-color: unset;
    position: absolute;
    left: 0;
    top: 2.75rem;
}
.modal__body  > h6{
    color: black;
    font-size: 15px;
    font-weight: 700;
}
.modal__body  > p{
    font-size: 14px;
    font-weight: 500;
}
.modal__footer__actions{
    border-radius: 0.375rem ;
    justify-content: center;
}
.confirm{
    background-color: #635BFE !important  ;
}
.danger{
    border: 1px solid #FFC2C2;
    background-color: #FEEBEB !important  ;
}
.modal__footer__actions:focus ,.modal__footer__actions:active  {
    box-shadow: unset;
}
.form-group{
    margin: 0  ;
}
.form-group label{
    color: #344054;
}
.form-group > input{
    border-radius: 8px;
}
 .form-group span {
    position: absolute;
    left: 0.25rem;
    bottom: -35px;
    font-size: 14px;
    font-weight: 600 !important;
}

.custom-form-t .form-control{
    color: #414141;
}
.custom-form-t .form-control::placeholder{
    color: #ADADAD;
}


[dir="rtl"] .form-group span {
    left: unset;
    right: 0.25rem
}

.form-group span#currency {
    top: 50px;
    left: 92%;
}
[dir='rtl'] .form-group span#currency {
    right: 92%;
}

.card-type{
    font-size: 14px;
    color: #344054;
    font-weight: 500;
}


.card_box button {
    border: 1px solid #D0D5DD;
    border-radius: 8px;
    width: 110px !important
}

.recharge_limits p{
    color: #555555;
    font-size: 14px;
    font-weight: 500;
}
.modal__body p{
    font-size: 14px;
    font-weight: 500;
    color: #344054;
}
.modal__body__info  p{
    color: #635BFE;
    font-size: 12px;
    font-weight: 500;

}
.border--primary{
    box-shadow: 0px 0px 1px 1px #635bfea1;
  }
  .card__details_des{
    border-radius: 8px;
  }

.one_card{
    transition: all .3s;
}
.one_card:hover{
    background-color: #635bfe27;
  }
  .rotate_arrow{
    transform: rotate(90deg);
    transition: all .3s;
    scale: .70;
}

[dir='ltr'] .rotate_arrow{
    transform: rotate(-90deg);
}

.one_card:hover .rotate_arrow{
    transform: translateX(-10px) rotate(90deg);
    transition: all .3s;
  }
  [dir='ltr'] .one_card:hover .rotate_arrow{
    transform: translateX(10px) rotate(-90deg);
  }

  .text-size-12{
    font-size: 12px;
  }
  .text-size-14{
    font-size: 14px;
  }

  .one_detail{
    background-color: #E9E9E9; 
  }
  .card_margin_inline_start{
    margin-inline-start: auto
  }
  @media (width<1200px) {
      .card_margin_inline_start{
        margin-inline-start: unset
      }
  }