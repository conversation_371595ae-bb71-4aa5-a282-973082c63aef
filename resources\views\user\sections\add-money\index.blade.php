@extends('user.layouts.master')

@push('css')
    <link rel="stylesheet" href="{{ asset('frontend/css/custom/deposit-widthdraw.css') }}" />
    <link rel="stylesheet" href="{{ asset('frontend/css/custom/transaction-history.css') }}" />
@endpush

@section('breadcrumb')
    @include('user.components.breadcrumb',['breadcrumbs' => [
        [
            'name'  => __("Dashboard"),
            'url'   => setRoute("user.dashboard"),
        ]
    ], 'active' => __(@$page_title)])
@endsection

@section('content')
<div class="deposit-withdraw">
    <!-- Add Money Card Start -->
      <section class="border--primary p-2 rounded-3">
        <div class="row">
          <!-- Right Card Start -->
          <div class="col-md-6 mb-3">
            <p>{{ __('Add Money') }}</p>

            <!-- Payment Method Select Component -->
            <form class="bg--gray p-3 rounded-3" id="payment-step-one" data-submit-url="{{ route('user.add.money.submit') }}">
                <div>
                    <label for="payment-gateway" class="mb-2">{{ __('Payment Gateway') }}</label>
                    <fieldset>
                        <select class="form-select" name="gateway" id="option_icon">
                            @foreach($payment_gateways_currencies as $gateway)
                                <option
                                    value="{{ $gateway->alias }}"
                                    data-class="avatar"
                                    data-style="background-image: url('{{ asset('frontend/icons/' . $gateway->slug . '.svg') }}');"
                                >
                                    {{ $gateway->name }}
                                </option>
                            @endforeach
                        </select>
                        <div class="selected--option--icon">
                            <img src="{{ asset('frontend/assets/icons/usdt-active.svg') }}" />
                        </div>
                        <div class="dropdown__icon">
                                    <img src="{{ asset('frontend/assets/icons/Dropdown.svg') }}" />
                                </div>
                    </fieldset>
                </div>

                <div class="mt-2">
                    <label for="payment-gateway" class="mb-2">{{ __('Available Networks') }}</label>
                    <fieldset>
                        <select class="form-select" name="network" id="networks-list">
                          @foreach ($networks as $network)
                            <option value="{{ $network->network_id }}">{{ $network->network_name }}</option>
                          @endforeach
                        </select>
                        <div class="dropdown__icon">
                                    <img src="{{ asset('frontend/assets/icons/Dropdown.svg') }}" />
                                </div>
                    </fieldset>
                </div>
                <p class='text--primary balance__text'>{{ __('Charge') }}:  <span class='text--primary balance__text' dir="ltr">{{ $PaymentGatewayCurrency->fixed_charge }} USD</span> + {{ $PaymentGatewayCurrency->percent_charge }}%</p>

                <button id="confirm-btn" type="button" class="confirm-btn--color rounded-3 text-center w-100 py-2 fw-bold">
                    {{ __('Confirm') }}
                </button>
            </form>

            <div class="col-md-6 p-3 bg--gray w-auto d-flex flex-column justify-content-center align-items-center rounded-3 d-none" id="payment-step-two">
                <div class="d-flex justify-content-between align-items-center w-100">
                  <p class="text-center">{{ __('Scan the QR code to complete the transfer') }}</p>
                  <button id="back-btn" class="bg-transparent">
                    <img src="{{ asset('frontend/assets/icons/right-arrow.svg') }}" alt="{{ __('Back') }}" class="mb-3" />
                  </button>
                </div>
                <img id="qr-code" src="" alt="{{ __('QR code') }}" class="d-block mb-4" />
                <div class="text-center">
                  <div class="d-flex align-items-center gap-3 justify-content-center">
                    <span style="word-break: break-all;" id="wallet-address"></span>
                    <button id="copy-btn">
                      <img src="{{ asset('frontend/assets/icons/copy.svg') }}" alt="{{ __('Copy') }}" />
                    </button>
                  </div>
                  <p class="mt-2">
                    {{ __('Minimum deposit') }}
                    <span id="min-deposit" class="text--primary fw-bold mx-1">{{ $PaymentGatewayCurrency->min_limit }} USDT</span>
                  </p>
                </div>
              </div>

          </div>
          <!-- Right Card End -->

          <!-- Left Card Start -->
          <div class="col-md-6 mb-3" id="notes">
            <p>{{ __('Notes') }}</p>
            <div class="bg--gray p-3 rounded-3">
              <section class="notes-section">
                <ul class="p-3">
                  <li>{{ __('usdt_deposit_note1') }} {{ $gateway->name }}</li>
                  <li>{{ __('usdt_deposit_note2_v1') }} {{ $gateway->name }} {{ __('usdt_deposit_note2_v2') }}</li>
                  <li >{{ __('usdt_deposit_note3_v1') }} <span id="notes_network_name">{{ isset($networks[0]) ? $networks[0]->network_name : '' }}</span> {{ __('usdt_deposit_note3_v2') }}</li>
                  <li>{{ __('usdt_deposit_note4') }}</li>
                  <li>{{ __('usdt_deposit_note5') }}</li>
                  <li>{{ __('usdt_deposit_note6') }}</li>
                  <li>{{ __('usdt_deposit_note7') }}</li>

                </ul>
              </section>
            </div>
          </div>
          <!-- Left Card End -->
        </div>
      </section>
      <!-- Add Money Card End -->

      <!-- transaction history section start -->
      <section class="transactions d-flex flex-column mt-4">
        <div class="transactions__header">
          <p>{{ __('Add Money Transaction History') }}</p>
        </div>
        <ul class="timeline w-100 dashboard-list-wrapper">
            @include('user.components.transaction-log',compact("transactions"))
        </ul>
      </section>
    </div>
@endsection

@push('script')

<script>
document.getElementById('confirm-btn').addEventListener('click', function () {
    var formData = new FormData(document.getElementById('payment-step-one'));

    fetch('{{ route('user.add.money.submit') }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.data) {
            // Update the wallet address
            document.getElementById('wallet-address').textContent = data.data.address;
            document.getElementById('qr-code').src = data.data.qr_code;

            // Store the address in a variable for later use
            window.copiedAddress = data.data.address;

            // Show the second step and hide the first step
            document.getElementById('payment-step-one').classList.add('d-none');
            document.getElementById('payment-step-two').classList.remove('d-none');
        } else {
            console.error('{{ __("Unexpected response structure:") }}', data.message.error);
            toastr.error('{{ __("Unexpected response structure:") }}', data.message.error);

        }
    })
    .catch(error => {
      toastr.error('{{ __("An error occurred. Please try again.") }}');

        // console.error('{{ __("Error:") }}', error);
    });
});

document.getElementById('copy-btn').addEventListener('click', function () {
    if (window.copiedAddress) {
        // Create a temporary input element
        var tempInput = document.createElement("input");
        document.body.appendChild(tempInput);
        tempInput.value = window.copiedAddress;
        tempInput.select();
        // Copy the text to the clipboard
        var successful = document.execCommand("copy");
        document.body.removeChild(tempInput);

        if (!successful) {
            // this.style.display = 'none';
            console.error('{{ __("Failed to copy text.") }}');
        } else {
        }
    } else {
        console.error('{{ __("No address to copy") }}');
    }
});

document.getElementById('back-btn').addEventListener('click', function () {
    // Hide the second step and show the first step
    document.getElementById('payment-step-two').classList.add('d-none');
    document.getElementById('payment-step-one').classList.remove('d-none');
});

document.getElementById('networks-list').addEventListener('change', function (e) {
  document.getElementById('notes_network_name').textContent = document.getElementById('networks-list').value 

})

</script>

<script src="{{ asset('frontend/js/custom/transaction-history.js') }}"></script>

@endpush
