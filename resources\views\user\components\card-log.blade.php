@if(empty($transactions))
    <div class="empty-transaction-card">
        <img width='127' src="{{ asset('frontend/assets/no-transactions.svg') }}" alt="transactions">
        <p class="text-center text-muted">{{ __('no_current_transactions') }}</p>
    </div>
@else
    @foreach ($transactions as $item)
        <li class="timeline-item transaction d-flex align-content-start gap-6 item-wrapper">
            <div class="d-flex flex-column">
                <div class="transaction__icon">
                    @if($item['amount_full']>0)
                    <img src="{{ asset('frontend/assets/images/deposit.svg') }}" alt="transaction-icon" />
                    @else
                    <img src="{{ asset('frontend/assets/images/withdraw.svg') }}" alt="transaction-icon" />
                    @endif
                    
                </div>
            </div>
            <div class="transaction__card flex-grow-1">
                <div class="d-flex justify-content-between">
                    <p class="text-dark fw-medium m-0">

                      <span class="text--primary">{{ $item['id'] }}</span>

                    </p>
                    <p class="transaction__card__date text-dark fw-medium mb-0">
                        {{ $item['created_at'] }}
                    </p>
                </div>
                <div class="d-flex justify-content-between">
                    <div class="transaction__card__status fw-bolder d-flex gap-1">
                        <p class="transaction__card__status__icon --success"></p>
                        <p class="transaction__card__status__text mb-0 fw-bold">
                            ناجحة
                        </p>
                    </div>
                    <p class="transaction__card__amount fw-medium text--primary mb-0">
                        ${{ $item['amount_full'] }}
                    </p>
                </div>
         
                
            </div>
        </li>
    @endforeach
@endif
