<!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Start Header
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->
@php
    $type = App\Constants\GlobalConst::SETUP_PAGE;
    $menues = DB::table('setup_pages')
            ->where('status', 1)
            ->where('type', Str::slug($type))
            ->get();
@endphp
{{-- <header class="header-section">
    <div class="header">
        <div class="header-bottom-area">
            <div class="container custom-container">
                <div class="header-menu-content">
                    <nav class="navbar navbar-expand-lg p-0">
                        <a class="site-logo site-title" href="{{ setRoute('index') }}">
                            <img src="{{ get_logo($basic_settings) }}"  data-white_img="{{ get_logo($basic_settings,'white') }}"
                            data-dark_img="{{ get_logo($basic_settings,'dark') }}"
                                alt="site-logo">
                        </a>

                        <button class="navbar-toggler ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="fas fa-bars"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarSupportedContent">
                            <ul class="navbar-nav main-menu ms-auto">
                                @php
                                $current_url = URL::current();
                            @endphp
                            @foreach ($menues as $item)
                                @php
                                    $title = json_decode($item->title);
                                @endphp
                                <li><a href="{{ url($item->url) }}" class="@if ($current_url == url($item->url)) active @endif">{{ __($title->title) }}</a></li>
                            @endforeach
                            </ul>
                            @php
                                $session_lan = session('local')??get_default_language_code();
                            @endphp
                            <select class="language-select langSel">
                                @foreach($__languages as $item)
                                    <option value="{{$item->code}}" @if( $session_lan == $item->code) selected  @endif>{{ $item->name }}</option>
                                @endforeach
                            </select>
                            <div class="header-action">
                                @auth
                                    @if(auth()->user()->email_verified == 0)
                                    <button class="btn--base header-account-btn">{{ __("Login Now") }}</button>
                                    @else
                                     <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{__("Dashboard")}}</a>
                                    @endif

                                @else
                                <button class="btn--base header-account-btn">{{ __("Login Now") }}</button>
                                @endauth
                            </div>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</header> --}}


<nav class="navbar navbar-expand-lg py-4">
  <div class="container-fluid">
    <div class="navbar-brand mx-auto">
      <a href="{{ setRoute('index') }}">
        <img src="{{ asset('frontend/assets/logo.svg') }}" alt="Card App" />
      </a>
    </div>
    <div class="collapse navbar-collapse" id="navbarSupportedContent" style='' >
      <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a href="{{ setRoute('index') }}" class="nav-link active" id=''>{{ __('Home') }}</a>
        </li>
        <li class="nav-item">
          <a href="{{ setRoute('about') }}" class="nav-link" id='about'>{{ __('About Us') }}</a>
        </li>
        <li class="nav-item">
          <a href="{{ setRoute('services') }}" class="nav-link" id='services'>{{ __('Our Services') }}</a>
        </li>
        <li class="nav-item">
          <a href="{{ setRoute('contact') }}" class="nav-link" id='contact'>{{ __('CONTACT US') }}</a>
        </li>
        @php
        $session_lan = session('local')??get_default_language_code();
        @endphp

        <!-- language start -->
        <div class="language-dropdown">
    <button class="dropdown-btn">
    <img width='20' src="{{ asset('frontend/assets/icons/language-switcher.svg') }}" alt="language switcher">
    </button>
    <ul class="dropdown-menu p-2">
        @foreach($__languages as $item)
        <li class='nav-item'>
            <a href="{{ route('lang', ['lang' => $item->code ]) }}" data-value="{{$item->code}}" style="width:100%;margin:5px" class="@if( $session_lan == $item->code) selected @endif">{{ $item->name }}</a>
        </li>
        @endforeach
    </ul>
</div>
<!-- language end -->

      </ul>
      <div class="header-action">
        @auth
            @if(auth()->user()->email_verified == 0)
            <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
            @else
             <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{ __('Dashboard') }}</a>
            @endif

        @else
        <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
        @endauth
    </div>
    </div>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon">=</span>
    </button>
  </div>
</nav>

