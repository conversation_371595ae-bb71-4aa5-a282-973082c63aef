{"__meta": {"id": "X620203de77a0abb03869ef8cbe32de28", "datetime": "2025-06-22 14:48:24", "utime": **********.676356, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.297546, "end": **********.676373, "duration": 0.3788270950317383, "duration_str": "379ms", "measures": [{"label": "Booting", "start": **********.297546, "relative_start": 0, "end": **********.588351, "relative_end": **********.588351, "duration": 0.2908051013946533, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.588373, "relative_start": 0.29082703590393066, "end": **********.676375, "relative_end": 1.9073486328125e-06, "duration": 0.08800196647644043, "duration_str": "88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25773656, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "frontend.index", "param_count": null, "params": [], "start": **********.63287, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/index.blade.phpfrontend.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "frontend.layouts.master", "param_count": null, "params": [], "start": **********.640552, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.phpfrontend.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "partials.header-asset", "param_count": null, "params": [], "start": **********.654075, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/header-asset.blade.phppartials.header-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Fheader-asset.blade.php&line=1", "ajax": false, "filename": "header-asset.blade.php", "line": "?"}}, {"name": "frontend.partials.header", "param_count": null, "params": [], "start": **********.657044, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.phpfrontend.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "frontend.partials.account-info", "param_count": null, "params": [], "start": **********.660977, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/account-info.blade.phpfrontend.partials.account-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Faccount-info.blade.php&line=1", "ajax": false, "filename": "account-info.blade.php", "line": "?"}}, {"name": "frontend.partials.footer", "param_count": null, "params": [], "start": **********.664413, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.phpfrontend.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "partials.footer-asset", "param_count": null, "params": [], "start": **********.671195, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/partials/footer-asset.blade.phppartials.footer-asset", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fpartials%2Ffooter-asset.blade.php&line=1", "ajax": false, "filename": "footer-asset.blade.php", "line": "?"}}, {"name": "admin.partials.notify", "param_count": null, "params": [], "start": **********.671946, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/admin/partials/notify.blade.phpadmin.partials.notify", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Fadmin%2Fpartials%2Fnotify.blade.php&line=1", "ajax": false, "filename": "notify.blade.php", "line": "?"}}, {"name": "frontend.partials.extensions.tawk-to", "param_count": null, "params": [], "start": **********.672709, "type": "blade", "hash": "bladeD:\\projects\\CardApp\\resources\\views/frontend/partials/extensions/tawk-to.blade.phpfrontend.partials.extensions.tawk-to", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fextensions%2Ftawk-to.blade.php&line=1", "ajax": false, "filename": "tawk-to.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\SiteController@home", "namespace": null, "prefix": "", "where": [], "as": "index", "file": "<a href=\"phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=18\" onclick=\"\">app/Http/Controllers/SiteController.php:18-22</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0058, "accumulated_duration_str": "5.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.617323, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Localization.php:22", "source": {"index": 15, "namespace": null, "name": "app/Http/Middleware/Admin/Localization.php", "file": "D:\\projects\\CardApp\\app\\Http\\Middleware\\Admin\\Localization.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FMiddleware%2FAdmin%2FLocalization.php&line=22", "ajax": false, "filename": "Localization.php", "line": "22"}, "connection": "cardapp", "explain": null, "start_percent": 0, "width_percent": 9.483}, {"sql": "select * from `basic_settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\SiteController.php", "line": 19}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6223722, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "SiteController.php:19", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/SiteController.php", "file": "D:\\projects\\CardApp\\app\\Http\\Controllers\\SiteController.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FControllers%2FSiteController.php&line=19", "ajax": false, "filename": "SiteController.php", "line": "19"}, "connection": "cardapp", "explain": null, "start_percent": 9.483, "width_percent": 7.586}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.641241, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1588", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1588", "ajax": false, "filename": "helpers.php", "line": "1588"}, "connection": "cardapp", "explain": null, "start_percent": 17.069, "width_percent": 8.621}, {"sql": "select * from `site_sections` where `key` = 'site_cookie' limit 1", "type": "query", "params": [], "bindings": ["site_cookie"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Admin/SiteSections.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\SiteSections.php", "line": 19}, {"index": 24, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 4}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.643674, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SiteSections.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/Admin/SiteSections.php", "file": "D:\\projects\\CardApp\\app\\Models\\Admin\\SiteSections.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSiteSections.php&line=19", "ajax": false, "filename": "SiteSections.php", "line": "19"}, "connection": "cardapp", "explain": null, "start_percent": 25.69, "width_percent": 7.414}, {"sql": "select * from `setup_seos` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 5}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.64531, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.master:5", "source": {"index": 18, "namespace": "view", "name": "frontend.layouts.master", "file": "D:\\projects\\CardApp\\resources\\views/frontend/layouts/master.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmaster.blade.php&line=5", "ajax": false, "filename": "master.blade.php", "line": "5"}, "connection": "cardapp", "explain": null, "start_percent": 33.103, "width_percent": 6.552}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.652065, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 39.655, "width_percent": 9.138}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6546931, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1597", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1597", "ajax": false, "filename": "helpers.php", "line": "1597"}, "connection": "cardapp", "explain": null, "start_percent": 48.793, "width_percent": 8.103}, {"sql": "select * from `setup_pages` where `status` = 1 and `type` = 'setup-page'", "type": "query", "params": [], "bindings": [1, "setup-page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": "view", "name": "frontend.partials.header", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.php", "line": 9}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6580591, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.header:9", "source": {"index": 12, "namespace": "view", "name": "frontend.partials.header", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/header.blade.php", "line": 9}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fheader.blade.php&line=9", "ajax": false, "filename": "header.blade.php", "line": "9"}, "connection": "cardapp", "explain": null, "start_percent": 56.897, "width_percent": 11.724}, {"sql": "select * from `languages` where `status` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.665101, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "helpers.php:1588", "source": {"index": 15, "namespace": null, "name": "app/Http/Helpers/helpers.php", "file": "D:\\projects\\CardApp\\app\\Http\\Helpers\\helpers.php", "line": 1588}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FHttp%2FHelpers%2Fhelpers.php&line=1588", "ajax": false, "filename": "helpers.php", "line": "1588"}, "connection": "cardapp", "explain": null, "start_percent": 68.621, "width_percent": 11.552}, {"sql": "select * from `site_sections` where `key` = 'footer-section' limit 1", "type": "query", "params": [], "bindings": ["footer-section"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.6671848, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.footer:5", "source": {"index": 15, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 5}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=5", "ajax": false, "filename": "footer.blade.php", "line": "5"}, "connection": "cardapp", "explain": null, "start_percent": 80.172, "width_percent": 10.172}, {"sql": "select * from `setup_pages` where `type` = 'useful-links' and `status` = 1 order by `id` asc", "type": "query", "params": [], "bindings": ["useful-links", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 7}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\projects\\CardApp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.669299, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "frontend.partials.footer:7", "source": {"index": 14, "namespace": "view", "name": "frontend.partials.footer", "file": "D:\\projects\\CardApp\\resources\\views/frontend/partials/footer.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Ffooter.blade.php&line=7", "ajax": false, "filename": "footer.blade.php", "line": "7"}, "connection": "cardapp", "explain": null, "start_percent": 90.345, "width_percent": 9.655}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\SiteSections": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSiteSections.php&line=1", "ajax": false, "filename": "SiteSections.php", "line": "?"}}, "App\\Models\\Admin\\BasicSettings": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FBasicSettings.php&line=1", "ajax": false, "filename": "BasicSettings.php", "line": "?"}}, "App\\Models\\Admin\\SetupSeo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSetupSeo.php&line=1", "ajax": false, "filename": "SetupSeo.php", "line": "?"}}, "App\\Models\\Admin\\SetupPage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fprojects%2FCardApp%2Fapp%2FModels%2FAdmin%2FSetupPage.php&line=1", "ajax": false, "filename": "SetupPage.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1292086444 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1292086444\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-416467328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416467328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-229852168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-229852168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-507322688 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ar;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlVhRitKRUhKcW5ieWZvczMvZDBuTlE9PSIsInZhbHVlIjoiQjZVSVE2TXFPUUpDZWpoaEFkSXEvRmZUNzdlb3I3ZjlkUTV2ZURkUFhhNFJtazhHVVlzUmZKQTk4Q2tRaTYxaVh3QVFEbkV4L1Q5NlZpTDkwRkxrMzc0OFNLczRhZWhpWGdFYkNSSkZrZGVEZWpsRHBnMnNuSFlMN2l3ZmlKdXEiLCJtYWMiOiJkNzg2ZDIxODUwM2JjYjYwM2FkZTY2OTQzNGY4MmNkZGI4YjVhY2NkMGEwN2Q5ZjU2ZDhmMDg5M2RiOWY1Y2YwIiwidGFnIjoiIn0%3D; appdevs_session=eyJpdiI6Im9vRjV4WTFpazcvQ1lnVXM5TGNPUXc9PSIsInZhbHVlIjoiWUREblhWM1V6eHpRQUpHNEk4VUtIa0c2OTFQdWtab2dCR3orb2NsVVVJeFBOK3ZDd0xHQk5IVFB2UStpc0tNcHFhZC90RVFPSnVmTXpkaFBnQm0xRXlSeHppMlAzN0hqTkRPR1hvaHljVkROT1Vmc3JGajhBTE9YMC9NeUdNaEoiLCJtYWMiOiI5ZjE5OWI1MjEyYjVkYWU5MzA4YjQxODI4ODNlZTNlMjYzZjUyNDUyMDQzZTQ3NWZkNDk1NThiYzI4YzI1N2JkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507322688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2121815577 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>appdevs_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RygapEhuNHzIvKDgi3fvYLTxPvSb5kpnIslLDxGP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121815577\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1885781604 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 22 Jun 2025 08:48:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im9HR29tejdtZURUUmlFWGpZbXRHUFE9PSIsInZhbHVlIjoiVmZOdy9RMUJCc3I4anFyS0F5MTZLcVVrVUo4T0RVanNtS0hYTnJOaHdMVnFzL2pqNlNKcThxTG1wVzI0bis0TElnT0pUK1pHT2RPL3RYU1RuUlZlQXdlSHVLa2tCb3BKekYzdzROWkRuczN2d2F0YXBWLzR3M2dEcHY1cy84emoiLCJtYWMiOiIzYTVkODM5OTRmNTk5MjhhODg2ZjYzZGM0ZGVkNjg3ZjFkMTJmMzNhM2I2YWQ4NDFjZTg1ZWY3Y2RkZTkzNDJmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:48:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">appdevs_session=eyJpdiI6IkYyb2NRWmZ2RFlRTzFFa1hjazRnNnc9PSIsInZhbHVlIjoiVTRUMDIwcU9uRDRIWTc4NnVPanAvZ2IzSVR1QUZpMzlDYllEOHBCdlZRb2phelN1VDlESUlGRm1iWEJETW1sMXRXMVdXMDBaSk5VMnNFdWlqNW1qWnFvRExBRkUrYjcvbW15L01jQU5BcGJ6Snc2SjZPbi9qYUJEdzVRdm5NNkMiLCJtYWMiOiJjZTU2OWZjNDEwZmJhNDRkNTliMDYyNzM4ZDFkNzJlMTU0NGUzMjlmOWI4MDNjZjYxMDIwN2M0NWNlYzRlMjVlIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:48:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im9HR29tejdtZURUUmlFWGpZbXRHUFE9PSIsInZhbHVlIjoiVmZOdy9RMUJCc3I4anFyS0F5MTZLcVVrVUo4T0RVanNtS0hYTnJOaHdMVnFzL2pqNlNKcThxTG1wVzI0bis0TElnT0pUK1pHT2RPL3RYU1RuUlZlQXdlSHVLa2tCb3BKekYzdzROWkRuczN2d2F0YXBWLzR3M2dEcHY1cy84emoiLCJtYWMiOiIzYTVkODM5OTRmNTk5MjhhODg2ZjYzZGM0ZGVkNjg3ZjFkMTJmMzNhM2I2YWQ4NDFjZTg1ZWY3Y2RkZTkzNDJmIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:48:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">appdevs_session=eyJpdiI6IkYyb2NRWmZ2RFlRTzFFa1hjazRnNnc9PSIsInZhbHVlIjoiVTRUMDIwcU9uRDRIWTc4NnVPanAvZ2IzSVR1QUZpMzlDYllEOHBCdlZRb2phelN1VDlESUlGRm1iWEJETW1sMXRXMVdXMDBaSk5VMnNFdWlqNW1qWnFvRExBRkUrYjcvbW15L01jQU5BcGJ6Snc2SjZPbi9qYUJEdzVRdm5NNkMiLCJtYWMiOiJjZTU2OWZjNDEwZmJhNDRkNTliMDYyNzM4ZDFkNzJlMTU0NGUzMjlmOWI4MDNjZjYxMDIwN2M0NWNlYzRlMjVlIiwidGFnIjoiIn0%3D; expires=Sun, 22-Jun-2025 10:48:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885781604\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1674638572 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">g1ztrTUUHETHPjPL5giH4iUnd4Od4VtCQTkwqbPL</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674638572\", {\"maxDepth\":0})</script>\n"}}