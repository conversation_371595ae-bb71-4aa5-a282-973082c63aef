@if($transactions->isEmpty())
    <div class="empty-transaction-card">
        <img width='127' src="{{ asset('frontend/assets/no-transactions.svg') }}" alt="{{ __('transactions') }}">
        <p class="text-center text-muted">{{ __('no_current_transactions') }}</p>
    </div>
@else
    @foreach ($transactions as $item)
        @php
            // Translating the status
            switch ($item->status) {
                case 1:
                    $statusText = __('successful');
                    $statusClass = '--success';
                    break;
                case 2:
                    $statusText = __('pending');
                    $statusClass = '--pending';
                    break;
                case 3:
                    $statusText = __('on_hold');
                    $statusClass = '--hold';
                    break;
                case 4:
                    $statusText = __('rejected');
                    $statusClass = '--rejected';
                    break;
                default:
                    $statusText = __('failed');
                    $statusClass = '--failed';
                    break;
            }

            // Extract the part of the remark before "by"
            $remark = $item->remark;
            $position = strpos($remark, 'by');

            if ($position !== false) {
                $remarkBeforeBy = substr($remark, 0, $position); // Extract the part before "by"
            } else {
                $remarkBeforeBy = $remark; // Use the full remark if "by" is not found
            }

            $prefixes = ['TRANSFER MONEY From', 'TRANSFER MONEY To'];
            $translatedRemark = $remark;
            foreach ($prefixes as $prefix) {
                if (strpos($remarkBeforeBy, $prefix) === 0) {
                    $translatedRemark = __($prefix) . substr($remarkBeforeBy, strlen($prefix));
                    break;
                }
            }
        @endphp
        <li class="timeline-item transaction d-flex align-content-start gap-6 item-wrapper">
            <div class="d-flex flex-column">
                <div class="transaction__icon">
                    <img src="{{ asset('frontend/assets/images/' . $item->type . '.svg') }}" alt="{{ __('transaction-icon') }}" />
                </div>
            </div>
            <div class="transaction__card flex-grow-1">
                <div class="d-flex justify-content-between">
                    <p class="text-dark fw-medium m-0">
                        @if ($position)
                            {{ __($remarkBeforeBy) }} <span class="text--primary">{{ substr($remark, $position + 3) }}</span>
                        @else
                            <span class="text--primary">{{ __($translatedRemark) }}</span>
                        @endif
                    </p>
                    <p class="transaction__card__date text-dark fw-medium mb-0">
                        {{ $item->created_at }}
                    </p>
                </div>
                <div class="d-flex justify-content-between">
                    <div class="transaction__card__status fw-bolder d-flex gap-1">
                        <p class="transaction__card__status__icon {{ $statusClass }}"></p>
                        <p class="transaction__card__status__text mb-0 fw-bold">
                            {{ __($statusText) }}
                        </p>
                    </div>
                    <p class="transaction__card__amount fw-medium text--primary mb-0">
                        ${{ $item->payable }}
                    </p>
                </div>
                <div class="transaction__card_details">
                    <div class="d-flex justify-content-between">
                        <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                            <img src="{{ asset('frontend/assets/images/tareget.svg') }}" />
                            <p class="transaction__card_details__row__text">
                                {{ __('transaction') }}
                            </p>
                        </span>
                        <p class="transaction__card_details__row__text">{{ $item->trx_id }}</p>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                            <img src="{{ asset('frontend/assets/images/bill.svg') }}" />
                            <p class="transaction__card_details__row__text">
                                {{ __('fees_and_charges') }}
                            </p>
                        </span>
                        <p class="transaction__card_details__row__text">{{ $item->charge->total_charge ?? null }}</p>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                            <img src="{{ asset('frontend/assets/images/purse.svg') }}" />
                            <p class="transaction__card_details__row__text">
                                {{ __('balance_after_transaction') }}
                            </p>
                        </span>
                        <p class="transaction__card_details__row__text">{{ $item->available_balance }}</p>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                            <img src="{{ asset('frontend/assets/images/clock.svg') }}" />
                            <p class="transaction__card_details__row__text">
                                {{ __('time_and_date') }}
                            </p>
                        </span>
                        <p class="transaction__card_details__row__text">{{ $item->created_at }}</p>
                    </div>
                    @if($item->reject_reason)

                    <div class="d-flex justify-content-between">
                            <span class="transaction__card_details__row d-flex gap-2 align-content-center">
                                <img src="{{ asset('frontend/assets/icons/rejection_reason.svg') }}" />
                                <p class="transaction__card_details__row__text">
                                   {{__("rejection reason")}}
                                </p>
                            </span>
                            <p class="transaction__card_details__row__text ">
                                {{$item->reject_reason}}
                            </p>
                    </div>
                    @endif

                </div>
                
            </div>
        </li>
    @endforeach
@endif
